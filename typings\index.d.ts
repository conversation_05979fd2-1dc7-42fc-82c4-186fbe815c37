/// <reference path="./types/index.d.ts" />

import { BluetoothManager } from "../miniprogram/lib/bluetoothManager";


interface IStoreOption {
  device: object, /* 设备信息 */
  serviceId: string, // 服务ID
  characterId: string, // 特征值ID
  writeId: string, // 写入ID
  notifyId: string, // 回复ID
  response: any,
  stopResponseFn: Function
}

interface IAppOption {
  globalData: {
    userInfo?: WechatMiniprogram.UserInfo,
    gbIsHandshake: boolean,
    gbIsAutoTest: boolean,
    gbTestNum: number,
    gbMtuNum: number,
    bluetoothManager: BluetoothManager,
    device: WechatMiniprogram.BlueToothDevice,
    connectGoToUrl: string,
    server_token:string;
    store: IStoreOption;
    version:String
  },
  stopResponseFn: Function,
  userInfoReadyCallback?: WechatMiniprogram.GetUserInfoSuccessCallback;
}