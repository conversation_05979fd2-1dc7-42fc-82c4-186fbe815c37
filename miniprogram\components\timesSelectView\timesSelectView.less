/* components/timesSelectView/timesSelectView.wxss */
.container {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.4);
}

.times-view{
  background-color: white;
  border-radius: 30rpx;

  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;

  position: fixed;
  height: 400rpx;
  left: 40rpx;
  right: 40rpx;
  top: 160rpx;
}
.content-view{
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;

  height: 70%;
  width: 100%;
  margin-top: 0rpx;
  margin-left: 0rpx;
  margin-right: 0rpx;
}
.view_1{
  width: 200rpx;
  height: 44rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  text-align: center;
  color: #242424;
  line-height: 44rpx;
  margin-bottom: -50rpx;
}
.input-view{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 560rpx;
  height: 96rpx;
  background: #EFEFEF;
  border-radius: 4px;
}
.input-view .sub_0{
  width: 70%;
  height: 100%;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  line-height: 44rpx;
  margin-left: 30rpx;
}
.input-view .sub_1{
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}

.btn-view{
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 30%;
  width: 100%;
  margin-top: 0rpx;
  margin-left: 0rpx;
  margin-right: 0rpx;
}
.btn-canel{
  width: 50%;
  height: 99%;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  text-align: center;
  color: #242424;
  line-height: 42rpx;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  border-top: 2rpx solid #F5F5F5;
  border-right: 2rpx solid #F5F5F5;
}
.btn-confirm{
  width: 50%;
  height: 99%;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  text-align: center;
  color: #398BFF;
  line-height: 42rpx;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-top: 2rpx solid #F5F5F5;
  border-left: 2rpx solid #F5F5F5;
}

.slider-view{
  height: 80rpx;
  line-height: 80rpx;
  font-size: 35rpx;  
  width: 100%;
  border-radius: 2rpx;
}

