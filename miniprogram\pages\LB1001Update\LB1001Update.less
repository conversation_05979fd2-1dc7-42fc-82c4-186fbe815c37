/* pages/pageUpdate/pageUpdate.wxss */
page {
  background-color: #F4F7FB;
}

.view_0{
  padding-left: 30rpx;
  padding-top: 30rpx;
}
.view_1{
  padding-left: 30rpx;
  padding-top: 30rpx;
}
.view_2{
  height: 520rpx;
  margin-top: 30rpx;
  margin-left: 30rpx;
  margin-right: 30rpx;
  background-color: white;
  border-radius: 16rpx;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.view_3{
  padding-top: 30rpx;
  height: 12%;
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.view_6{
  //border: solid red 1px;
  width: 100%;
  height: 88%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;

}
.view_4{
  margin: 0 auto; 
  margin-top:200rpx;
  height: 96rpx;
  width: 480rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 40rpx;
  background-color: #398BFF;
  border-radius: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.view_5{
  margin: 0 auto; 
  margin-top:200rpx;
  height: 96rpx;
  width: 480rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 40rpx;
  background-color: #D7DADD;
  border-radius: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.text_0{
  width: 240rpx;
  height: 40rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  line-height: 40rpx;
}
.text_1{
  width: 240rpx;
  height: 40rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #398BFF;
  line-height: 40rpx;
}
.text_2{
  width: 150rpx;
  height: 40rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  line-height: 40rpx;
}
.text_3{
  width: 150rpx;
  height: 40rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #398BFF;
  line-height: 40rpx;
}
.text_4{
  width: 300rpx;
  height: 40rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #242424;
  line-height: 40rpx;
  text-align: left;
}
.image_0{
  height: 48rpx;
  width: 48rpx;
}
.image_1{
  //border: solid red 1px;
  width: 308rpx;
  height: 212rpx;
}
.myOtaView_1{
  z-index: 2;
}


.file_scroll{
  //border-color: solid blue 2rpx;
  height: 90%;
  width: 92%;
}
.itemView{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border-bottom: solid #F4F7FB 2rpx;
  height: 128rpx;
  padding-left: 0rpx;
  padding-right: 0rpx;
  word-break: keep-all;
}
.view_left{
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  height: 60rpx;
  width: 90%;
}
.image_l{
  height: 48rpx;
  width: 48rpx;
}
.text_l{
  //border: solid red 1px;
  width: 90%;
  height: 10rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500rpx;
  color: #242424;
  line-height: 10rpx;
  text-align: left;
  margin-left: 20rpx;
  word-break: keep-all;
}
.view_right{
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 36rpx;
  height: 36rpx;
}
.image_r{
  width: 36rpx;
  height: 36rpx;
}