/* pages/pageFirmware/pageFirmware.wxss */


.body {
    height:67vh;
    width:100vw;
  }

  .foot {
    height:14vh;
    width:100vw;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .foot-img{
    width: 480rpx;
    height: 100rpx;
  }

  .other-btn{
       display: flex;
       flex-direction: row-reverse;
       margin-right: 20rpx;
  }

.not-device {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  
  .not-device-img {
    width: 370rpx;
    height: 350rpx;
  }
  .not-device-text{
      margin-top: 30rpx;
      padding-bottom: 200rpx;
  }

  .view_third{
    height: 99%;
    margin-top: 20rpx;
    overflow-y: auto;
  }

  .device-line {
    height: 170rpx;
    margin: 25rpx;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 25rpx;
    border:1rpx solid #dbd6d6;
    border-left: 3rpx solid #dbd6d6;
    border-bottom: 6rpx solid #dbd6d6;
  }
  .device-disable{
    height: 170rpx;
    margin: 25rpx;
    background-color: rgb(230, 230, 230);
    border-radius: 20rpx;
    padding: 25rpx;
    border:1rpx solid #dbd6d6;
    border-left: 3rpx solid #dbd6d6;
    border-bottom: 6rpx solid #dbd6d6;
  }

  .device-seled {
    height: 170rpx;
    margin: 25rpx;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 25rpx;
    border:1rpx solid #0394F0; 
    border-left: 3rpx solid #0394F0; 
    border-bottom: 6rpx solid #0394F0; 
  }

  .dev-left{
      display: flex;
      justify-content: space-between;
  }

  .dev-left-img{
      width: 40rpx;
      height: 40rpx;
  }
  
  .dev-right{
      padding-top: 10rpx;
      padding-left: 30rpx;
      font-size: 30rpx;
  }

  .dev-right-detail{
    overflow:hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow:ellipsis;  
  }

  .more-btn{
      display: inline;
  }



