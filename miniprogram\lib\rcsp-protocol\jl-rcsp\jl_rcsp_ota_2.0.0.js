"use strict";Object.defineProperty(exports,"__esModule",{value:!0});function e(e){let s=new Date;const n=s.getFullYear(),a=s.getMonth()+1,r=s.getDate(),i=s.getHours(),o=s.getMinutes(),c=s.getSeconds(),l=s.getMilliseconds();return`${[n,a,r].map(t).join("/")} ${[i,o,c,l].map(t).join(":")}`}const t=e=>(e=e.toString())[1]?e:`0${e}`;var s,n=3;function a(t){n<=1&&console.log(e(t))}function r(t){n<=5&&console.error(e(t))}function i(e){return Array.prototype.map.call(new Uint8Array(e),(function(e){return("00"+e.toString(16)).slice(-2)})).join("")}function o(e){let t="";for(let s=0;s<e.length;s++){t+=("00"+e[s].toString(16)).slice(-2),s!=e.length-1&&(t+=":")}return t.toUpperCase()}class c{}c.DEFAULT_SEND_CMD_TIMEOUT=3e3,c.DEFAULT_PROTOCOL_MTU=530,exports.Connection=void 0,(s=exports.Connection||(exports.Connection={}))[s.CONNECTION_DISCONNECT=0]="CONNECTION_DISCONNECT",s[s.CONNECTION_CONNECTING=1]="CONNECTION_CONNECTING",s[s.CONNECTION_CONNECTED=2]="CONNECTION_CONNECTED";class l{static getErrorDesc1(e){return this.getErrorDesc2(e,"")}static getErrorDesc2(e,t){let s="";switch(e){case l.ERROR_UNKNOWN:s="Unknown error.";break;case l.ERROR_NONE:s="Success";break;case l.ERROR_INVALID_PARAM:s="Invalid parameter.";break;case l.ERROR_DATA_FORMAT:s="Data formatting error.";break;case l.ERROR_NOT_FOUND_RESOURCE:s="No resources found.";break;case l.ERROR_UNKNOWN_DEVICE:s="Unknown device.";break;case l.ERROR_DEVICE_OFFLINE:s="Device went offline.";break;case l.ERROR_IO_EXCEPTION:s="I/O exceptions occur.";break;case l.ERROR_REPEAT_STATUS:s="Repeat state.";break;case l.ERROR_RESPONSE_TIMEOUT:s="Waiting for reply command timed out.";break;case l.ERROR_REPLY_BAD_STATUS:s="Device returned a bad status.";break;case l.ERROR_REPLY_BAD_RESULT:s="Device returned an error result.";break;case l.ERROR_NONE_PARSER:s="There is no associated parser."}return""==s||null==t||0==t.length?s:s+l.SEPARATOR+t}}l.ERROR_UNKNOWN=255,l.ERROR_NONE=0,l.ERROR_INVALID_PARAM=-1,l.ERROR_DATA_FORMAT=-2,l.ERROR_NOT_FOUND_RESOURCE=-3,l.ERROR_UNKNOWN_DEVICE=-32,l.ERROR_DEVICE_OFFLINE=-33,l.ERROR_IO_EXCEPTION=-35,l.ERROR_REPEAT_STATUS=-36,l.ERROR_RESPONSE_TIMEOUT=-64,l.ERROR_REPLY_BAD_STATUS=-65,l.ERROR_REPLY_BAD_RESULT=-66,l.ERROR_NONE_PARSER=-67,l.SEPARATOR="&_&";class h{convertToCmd(e){if(null==e)return null;let t=this.createCommand();if(null==t||t.getOpCode()!=e.getOpCode())return null;t.setCommand(e.isCommand()),t.setNeedResponse(e.isNeedResponse()),t.setReserve(e.getReserve());const s=e.getPayload();if(null!=s){let n;if(t.setPayload(s),n=e.isCommand()?t.getParam().parseData(s):null!=t.getResponse()?t.getResponse().parseData(s):l.ERROR_NONE_PARSER,n>=l.ERROR_NONE)return t}return null}}class u{constructor(){this._isCommand=!1,this._isNeedResponse=!1,this._reserve=0,this._opCode=0,this._sn=-1}isCommand(){return this._isCommand}setCommand(e){return this._isCommand=e,e||(this._isNeedResponse=!1),this}isNeedResponse(){return this._isNeedResponse}setNeedResponse(e){return this._isNeedResponse=e,this}getReserve(){return this._reserve}setReserve(e){return this._reserve=e,this}getOpCode(){return this._opCode}setOpCode(e){return this._opCode=e,this}getSn(){return this._sn}getPayload(){return this._payload}setPayload(e){if(this._payload=e,0!=e.byteLength){let t=0;this._isCommand||t++,e.byteLength>t&&(this._sn=e[t])}}toData(){let e=8,t=0;null!=this._payload&&(e+=this._payload.byteLength,t=this._payload.byteLength);let s=new Uint8Array(e);s[0]=254,s[1]=220,s[2]=186,s[e-1]=239;let n=0;this._isCommand&&(n|=128),this._isNeedResponse&&(n|=64),s[3]=n,s[4]=this._opCode;let a=this._isCommand?1:2;if(1==this._opCode&&a++,t<a)return r("RcspPacket: toData error: payload error."),null;if(s[5]=t>>8,s[6]=255&t,null!=this._payload){let e=new Uint8Array(this._payload);s.set(e,7)}return s}parseData(e){if(null==e||e.byteLength<=8)return r("RcspPacket: parseData : data is invalid param."),l.ERROR_INVALID_PARAM;for(let t=0;t<u.RCSP_HEAD.length;t++)if(u.RCSP_HEAD[t]!=e[t])return r("RcspPacket: parseData : head error, it is not rcsp data."),l.ERROR_DATA_FORMAT;let t=3,s=(255&e[t+2])<<8|255&e[t+3];if(s<1||s>e.byteLength-8)return r("RcspPacket: parseData : len error, it is not rcsp data."),l.ERROR_DATA_FORMAT;if(e[t+4+s]!=u.RCSP_END)return r("RcspPacket: parseData : end error, it is not rcsp data."),l.ERROR_DATA_FORMAT;const n=e[t];t++,this._isCommand=128==(128&n),this._isNeedResponse=64==(64&n),this._reserve=63&n,this._opCode=e[t],t++;const a=(255&e[t])<<8|255&e[t+1];t+=2;const i=new Uint8Array(a);for(let s=t;s<e.length;s++)i[s-t]=e[s];return this.setPayload(i),t+=a,t++,t}}u.RCSP_HEAD=[254,220,186],u.RCSP_END=239;class d extends u{constructor(e,t,s){super(),this.response=null,this.setOpCode(e),this.param=t,this.setResponse(s),this.setCommand(!0)}getParam(){return this.param}getResponse(){return this.response}getSn(){if(-1==super.getSn()){if(this.isCommand())return this.getParam().getSn();if(null!=this.getResponse())return this.getResponse().getSn()}return super.getSn()}setParam(e){this.param=e}setResponse(e){this.response=e,this.setNeedResponse(null!=e)}setSn(e){this.isCommand()?this.getParam().setSn(e):null!=this.getResponse()&&this.getResponse()?.setSn(e)}setStatus(e){null!=this.getResponse()&&this.getResponse()?.setStatus(e)}getStatus(){return null!=this.getResponse()?this.getResponse().getStatus():-1}toData(){let e;return this.isCommand()||this.setNeedResponse(!1),e=this.isCommand()?null==this.param?new Uint8Array(0):this.param.toData():null==this.response?new Uint8Array(0):this.response.toData(),this.setPayload(e),super.toData()}}class p{constructor(){this.sn=-1}setSn(e){this.sn=e}getSn(){return this.sn}getData(){return this.basePayload}setData(e){this.basePayload=e}toData(){let e=1;null!=this.basePayload&&(e+=this.basePayload.byteLength);let t=new Uint8Array(e);if(null!=this.sn?t[0]=this.sn:t[0]=0,null!=this.basePayload)for(let e=0;e<this.basePayload.byteLength;e++)t[e+1]=this.basePayload[e];return t}parseData(e){if(null==e||e.byteLength<1)return l.ERROR_INVALID_PARAM;if(this.sn=e[0],e.byteLength>1){let t=new Uint8Array;for(let s=1;s<e.length;s++)t[s-1]=e[s];this.basePayload=t}return 1}}class R{constructor(){this.status=-1,this.sn=-1}getStatus(){return this.status}getSn(){return this.sn}getPayload(){return this.payload}setStatus(e){this.status=e}setSn(e){this.sn=e}setPayload(e){this.payload=e}toData(){let e=2;null!=this.payload&&(e+=this.payload.byteLength);const t=new Uint8Array(e);if(t[0]=this.status,t[1]=this.sn,null!=this.payload)for(let e=0;e<this.payload.length;e++)t[e+2]=this.payload[e];return t}parseData(e){return e.byteLength<2?l.ERROR_INVALID_PARAM:(this.status=e[0],this.sn=e[1],e.byteLength>2&&(this.payload=new Uint8Array(e.length-2),this.payload.set(e.slice(2))),2)}}R.STATUS_UNKNOWN=255,R.STATUS_SUCCESS=0,R.STATUS_FAILED=1,R.STATUS_UNKNOWN_CMD=2,R.STATUS_BUSY=3,R.STATUS_NONE_RESOURCE=4,R.STATUS_CRC_ERROR=5,R.STATUS_ALL_DATA_CRC_ERROR=6,R.STATUS_INVALID_PARAM=7,R.STATUS_RESPONSE_DATA_OVER_LIMIT=8;class _ extends R{constructor(){super(...arguments),this.result=_.RESULT_OK}parseData(e){let t=super.parseData(e);return t<l.ERROR_NONE||e.length>=t+1&&(this.result=255&e[t],t++),t}toData(){const e=new Uint8Array;let t=0;const s=super.toData();return e.set(s,t),t+=s.length,e[t]=this.result,t+=1,e}}_.RESULT_OK=0,_.RESULT_FAIL=1;class D{constructor(e,t){this.device=e,this.type=t}}D.DATA_TYPE_SEND=0,D.DATA_TYPE_RECEIVE=1;class E extends D{constructor(e,t,s,n){super(e,D.DATA_TYPE_SEND),this.reSendCount=0,this.sendTimestamp=0,this.command=t,this.timeoutMs=s,this.callback=n}}class C extends D{constructor(e,t){super(e,D.DATA_TYPE_RECEIVE),this.data=t}}class g extends Array{}class T{constructor(e,t,s,n){this.SEND_AGAIN_LIMIT=3,this.isCanHandler=!1,this.ioProxy=e,this.listener=n,this.deviceMtuManager=t,this.dataInfoCache=new g,this.rcspParser=new O(s),this.sendInfoArray=new Array,this.receiveInfoArray=new Array,this.sendTimeOutIDMap=new Map}startHandler(){this.isCanHandler=!0,this.rcspParser.resetCache()}stopHandler(){this.isCanHandler=!1,this.dataInfoCache.length>0&&this.dataInfoCache.forEach((e=>{let t=e.callback;if(null!=t){let s=l.ERROR_IO_EXCEPTION;t.onError(e.device,s,l.getErrorDesc1(s))}})),this.sendTimeOutIDMap.size>0&&this.sendTimeOutIDMap.forEach((e=>{clearTimeout(e)})),this.dataInfoCache.length=0,this.sendInfoArray.length=0,this.receiveInfoArray.length=0,this.sendTimeOutIDMap.clear()}addSendData(e){if(this._checkDataAvailable(e)){if(this.sendInfoArray.push(e),this.sendInfoArray.length>1)return void a("RCSPDataHandler: 放入数据缓冲区，等待发送");const t={complete:()=>{this._writeDataToDevice(this.sendInfoArray,t)}};this._writeDataToDevice(this.sendInfoArray,t)}}addReceiveData(e){if(this._checkDataAvailable(e)){if(this.receiveInfoArray.push(e),this.receiveInfoArray.length>1)return void a("RCSPDataHandler: 放入数据缓冲区，等待解析");const t={complete:()=>{this._parseDataFromDevice(this.receiveInfoArray,t)}};this._parseDataFromDevice(this.receiveInfoArray,t)}}destroy(){this.stopHandler()}_checkDataAvailable(e){if(null==e){const e=l.ERROR_INVALID_PARAM;return this.listener.onError(null,e,l.getErrorDesc2(e,"DataInfo is null")),!1}if(!this._isCanHandle()){const t=l.ERROR_IO_EXCEPTION;return this.listener.onError(e.device,t,l.getErrorDesc2(t,"The processing thread is not working.")),!1}return!0}_isCanHandle(){return this.isCanHandler}_writeDataToDevice(t,s){const a=t.shift();if(null==a){if(0==t.length)return;return s.fail?.(),void s.complete?.()}if(!this._sendData(a))return r("RCSPDataHandler: send data failed. Please check whether the device is connected."),this._callbackError(a,l.ERROR_IO_EXCEPTION,"send data failed."),s.fail?.(),void s.complete?.();{let e=new Date;a.sendTimestamp=e.getMilliseconds()}if(a.command.isNeedResponse()){-1==this.dataInfoCache.indexOf(a)&&this.dataInfoCache.push(a);let t=setTimeout((t=>{const s=t,a=s.reSendCount;var r;r="RCSPDataHandler: "+s.command+", reSendCount: "+a+", limit: "+this.SEND_AGAIN_LIMIT,n<=4&&console.warn(e(r));const i=this.dataInfoCache.indexOf(s);if(i>-1&&this.dataInfoCache.splice(i,1),a<this.SEND_AGAIN_LIMIT)s.reSendCount=a+1,this.addSendData(s);else{const e="Command[%x]"+s.command.getOpCode();this._callbackError(s,l.ERROR_RESPONSE_TIMEOUT,e)}}),a.timeoutMs,a);const s=this._getSendDataKey(a);this.sendTimeOutIDMap.set(s,t)}else this._callbackCmd(a);s.success?.(),s.complete?.()}_sendData(e){const t=e.command.toData();if(null==t||0==t.byteLength)return a("RCSPDataHandler: sendData : data is null."),!1;if(t.byteLength>this._getRCSPSendMtu(e.device))return r("RCSPDataHandler: sendData : data over limit. RCSP mtu = "+this._getRCSPSendMtu(e.device)),!1;let s=!1;for(let n=0;n<3&&(s=this.ioProxy.sendDataToDevice(e.device,t),!s);n++);return s}_parseDataFromDevice(e,t){const s=e.shift();if(null==s){if(0==e.length)return;return t.fail?.(),void t.complete?.()}const n=this.rcspParser.findPacketData(this._getRCSPReceiveMtu(s.device),s.data);if(a("RCSPDataHandler: MSG_RECEIVE_DATA : commandList = "+JSON.stringify(n)),0==n.length)return t.fail?.(),void t.complete?.();n.forEach((e=>{if(e.isCommand()){const t=this.rcspParser.convertToCmd(e);if(null==t){const t=l.ERROR_NONE_PARSER;this.listener.onError(s.device,t,l.getErrorDesc2(t," Command op code = "+e.getOpCode()))}else this.listener.onRcspCommand(s.device,t)}else{const t=this._pollSendDataInfo(this.dataInfoCache,e);if(null==t)a("RCSPDataHandler: MSG_RECEIVE_DATA : no found cacheDataInfo.");else{a("RCSPDataHandler: MSG_RECEIVE_DATA : "+t);const n=this._getSendDataKey(t),r=this.sendTimeOutIDMap.get(n);null!=r&&(clearTimeout(r),this.sendTimeOutIDMap.delete(n));const i=t.command;if(null==i.getResponse())a("RCSPDataHandler: MSG_RECEIVE_DATA : no found command response.");else{const n=i.getResponse().parseData(e.getPayload());if(a("RCSPDataHandler: MSG_RECEIVE_DATA : ret = "+n),n>=l.ERROR_NONE)this.listener.onRcspResponse(s.device,i),this._callbackCmd(t);else{const s="The response of the command"+e.getOpCode()+" parse error. reason : "+n;a("RCSPDataHandler: "+s),this._callbackError(t,l.ERROR_DATA_FORMAT,s)}}}}}))}_callbackError(e,t,s){null!=e.callback&&e.callback.onError(e.device,t,l.getErrorDesc2(t,s)),this.listener.onError(e.device,t,l.getErrorDesc1(t))}_callbackCmd(e){null!=e.callback&&e.callback.onCmdResponse(e.device,e.command)}_getRCSPSendMtu(e){let t=this.deviceMtuManager.getReceiveMtu(e);return null!=t&&t>0?t:c.DEFAULT_PROTOCOL_MTU}_getRCSPReceiveMtu(e){let t=this.deviceMtuManager.getSendMtu(e);return null!=t&&t>0?t:c.DEFAULT_PROTOCOL_MTU}_getSendDataKey(e){if(null==e)return Number.MAX_VALUE;return e.command.getOpCode()<<16|e.command.getParam().getSn()}_pollSendDataInfo(e,t){if(0==e.length)return null;let s=null;if(e.forEach((e=>{t.isCommand()||e.command.getOpCode()!=t.getOpCode()||t.getSn()!=e.command.getParam().getSn()||(s=e)})),null!=s){let t=e.indexOf(s);t>-1&&e.splice(t,1)}return s}}class O{constructor(e){this.cacheBuf=null,this.cacheLen=0,this.cmdParserMap=e}getCmdParserMap(){return this.cmdParserMap}convertToCmd(e){if(null==e)return null;let t=this.getCmdParser(e.getOpCode());return null==t?null:t.convertToCmd(e)}getCmdParser(e){return this.getCmdParserMap().get(e)}resetCache(){this.cacheLen>0&&(this.cacheLen=0)}findPacketData(t,s){if(0==t||null==s||0==s.length)return new Array;const i=new Array,o=this._mergeCacheData(s),c=o.length;let h=0,d=0;for(a("RcspParser: findPacketData : mtu = "+t);d<c;){if(h=this._findValidRcspHeadIndex(o,d,t),-1==h){p="RcspParser: findPacketData : not find head data.",n<=3;break}a("RcspParser: findPacketData : prefixIndex = "+h);const s=(255&o[h+5])<<8|255&o[h+6],c=new Uint8Array(8+s);for(let e=0;e<c.length;e++)c[e]=o[h+e];const R=new u,_=R.parseData(c);_>l.ERROR_NONE?(i.push(R),d=h+c.length):(r("RcspParser: findPacketData : parse data error.  code = "+_+", skip"),d++)}var p;return i}_findValidRcspHeadIndex(e,t,s){if(null==e||0==e.byteLength)return-1;const n=e.byteLength;let r=n-t;if(r<=8)return a("RcspParser: findValidRcspHeadIndex : data is not enough. put data in cache."),this._putDataInCache(e,t,r),-1;let i=-1;for(let o=t;o<n;o++){if(e[o]!=u.RCSP_HEAD[0])continue;if(r=n-o,r<=8){this._putDataInCache(e,o,r);break}let t=!0;for(let s=0;s<u.RCSP_HEAD.length;s++)e[s+o]!=u.RCSP_HEAD[s]&&(t=!1);if(!t)continue;const c=o+u.RCSP_HEAD.length,l=(255&e[c+2])<<8|255&e[c+3];if(l<1||l>s-8){a("RcspParser: findPacketData :: data length["+l+"] over MAX_RECEIVE_MTU["+s+"], cast away"),o=c-1;continue}if(r<l+8){this._putDataInCache(e,o,r);break}if(e[c+4+l]==u.RCSP_END){i=o;break}o=c-1}return i}_mergeCacheData(e){if(null==e)return new Uint8Array(0);const t=e.byteLength;let s;if(this.cacheLen>0)s=new Uint8Array(this.cacheLen+t),this.cacheBuf&&s.set(this.cacheBuf),s.set(e,this.cacheLen),this.cacheLen=0;else{s=new Uint8Array(t);for(let t=0;t<e.length;t++)s[t]=e[t]}return s}_putDataInCache(e,t,s){if(null!=e&&e.byteLength>0&&t>=0&&s>0&&t+s<=e.byteLength){const n=e.slice(t,t+s);this.cacheBuf=n,this.cacheLen=s}}}class m{}m.CMD_UNKNOWN=0,m.CMD_DATA=1,m.CMD_GET_TARGET_FEATURE_MAP=2,m.CMD_GET_TARGET_INFO=3,m.CMD_DISCONNECT_CLASSIC_BLUETOOTH=6,m.CMD_GET_SYS_INFO=7,m.CMD_SET_SYS_INFO=8,m.CMD_SYS_INFO_AUTO_UPDATE=9,m.CMD_SWITCH_DEVICE_REQUEST=11,m.CMD_CUSTOM=240,m.CMD_EXTRA_CUSTOM=255,m.CMD_NOTIFY_DEVICE_APP_INFO=208,m.CMD_SETTINGS_COMMUNICATION_MTU=209,m.CMD_GET_DEV_MD5=212;class A{constructor(){this.cmdParserMap=new Map,this.cmdParserMap.set(m.CMD_DATA,new S),this.cmdParserMap.set(m.CMD_SYS_INFO_AUTO_UPDATE,new f),this.cmdParserMap.set(m.CMD_SETTINGS_COMMUNICATION_MTU,new I),this.cmdParserMap.set(m.CMD_CUSTOM,new M),this.cmdParserMap.set(m.CMD_EXTRA_CUSTOM,new N)}getCmdParserMap(){return this.cmdParserMap}}class S extends h{createCommand(){return new P(new v)}}class f extends h{createCommand(){return new y(new b)}}class I extends h{createCommand(){return new F(new V)}}class M extends h{createCommand(){return new B(new p)}}class N extends h{createCommand(){return new H(new p)}}class P extends d{constructor(e){super(m.CMD_DATA,e,null)}}class v extends p{toData(){const e=new Uint8Array;let t=0;const s=super.toData();return e.set(s,t),t+=s.length,null!=this.responseOpCode&&(e[t]=this.responseOpCode,t+=1),null!=this.payload&&this.payload.length>0&&(e.set(this.payload,t),t+=this.payload.length),e}parseData(e){let t=super.parseData(e);if(t<l.ERROR_NONE)return t;if(e.length<t+1)return l.ERROR_DATA_FORMAT;if(this.responseOpCode=255&e[t],t++,e.length>t){const s=new Uint8Array(e.length-t);s.set(e.slice(t)),t+=s.length,this.payload=s}return t}}class y extends d{constructor(e){super(m.CMD_SYS_INFO_AUTO_UPDATE,e,null)}}class U{getLen(){let e=0;return null!=this.value&&null!=this.value&&(e=this.value.byteLength+1),e}toData(){const e=new Uint8Array;return e[0]=this.getLen(),e[1]=null==this.type?0:this.type,null!=this.value&&this.value.length>0&&e.set(this.value,2),e}parseData(e){if(null==e||e.length<2)return l.ERROR_INVALID_PARAM;let t=0;const s=255&e[t];if(t++,s<1||s>e.length-t)return l.ERROR_DATA_FORMAT;if(this.type=255&e[t],t++,s-1>0){const n=new Uint8Array(s-1);n.set(e.slice(t,t+s-1)),t+=n.length,this.value=n}return t}}class b extends p{toData(){const e=new Uint8Array;let t=0;const s=super.toData();return e.set(s),t+=s.length,e[t]=null==this.function?0:this.function,t++,null!=this.dataList&&this.dataList.forEach((s=>{const n=s.toData();e.set(n,t),t+=n.length})),e}parseData(e){let t=super.parseData(e);if(t<l.ERROR_NONE)return t;if(e.length<t+1)return l.ERROR_DATA_FORMAT;this.function=255&e[t],t++;const s=e.length-t;if(s>0){const n=new Uint8Array(s);n.set(e.slice(t)),t+=n.length;let a=0;const r=new Array;for(;a+2<=n.length;){let t=new Uint8Array(n.length-a);t.set(e.slice(a,a+t.length));const s=new U,i=s.parseData(t);if(i<=0)break;r.push(s),a+=i}this.dataList=r}return t}}class k extends R{parseData(e){let t=super.parseData(e);if(t<l.ERROR_NONE)return t;if(e.length<t+1)return l.ERROR_DATA_FORMAT;this.function=255&e[t],t++;const s=e.length-t;if(s>0){const n=new Uint8Array(s);n.set(e.slice(t)),t+=n.length;let a=0;for(this.dataList=new Array;a+2<=n.length;){const e=new Uint8Array(n.length-a);e.set(n.slice(a,a+e.length));const t=new U,s=t.parseData(e);if(s<=0)break;this.dataList.push(t),a+=s}}return t}}class w extends d{constructor(e){super(m.CMD_GET_TARGET_INFO,e,new L)}}w.FLAG_MANDATORY_UPGRADE=1;class x extends p{constructor(e,t){super(),this.mask=e,this.platform=t}toData(){const e=super.toData(),t=new Uint8Array(e.length+5);let s=0;return t.set(e,0),s+=e.length,t[s]=this.mask>>24,t[s+1]=this.mask>>16&255,t[s+2]=this.mask>>8&255,t[s+3]=255&this.mask,t[s+4]=this.platform,t}}class L extends R{constructor(){super(...arguments),this.versionCode=0,this.sendMtu=c.DEFAULT_PROTOCOL_MTU,this.receiveMtu=c.DEFAULT_PROTOCOL_MTU,this.edrStatus=0,this.edrProfile=0,this.platform=-1,this.volume=0,this.maxVol=0,this.quantity=0,this.functionMask=0,this.curFunction=0,this.sdkType=0,this.pid=0,this.vid=0,this.uid=0,this.mandatoryUpgradeFlag=0,this.requestOtaFlag=0,this.ubootVersionCode=0,this.isSupportDoubleBackup=!1,this.isNeedBootLoader=!1,this.singleBackupOtaWay=0,this.expandMode=0,this.allowConnectFlag=0,this.bleOnly=!1,this.emitterSupport=!1,this.emitterStatus=0,this.isSupportMD5=!1,this.isGameMode=!1,this.isSupportSearchDevice=!1,this.supportOfflineShow=!1,this.supportUsb=!0,this.supportSd0=!0,this.supportSd1=!0,this.hideNetRadio=!1,this.supportVolumeSync=!1,this.supportSoundCard=!1,this.supportExternalFlashTransfer=!1,this.supportAnc=!1,this.banEq=!1,this.supportPackageCrc16=!1,this.getFileByNameWithDev=!1,this.contactsTransferBySmallFile=!1,this.watchSettingMask=0}parseData(e){let t=super.parseData(e);if(t<=l.ERROR_NONE)return t;if(e.length<t+2)return l.ERROR_INVALID_PARAM;const s=new Uint8Array(e.length-2);s.set(e.slice(2));let n=0,r=s.length;do{const e=255&s[n];if(n++,e<=0||e>r-n)return a("ResponseTargetInfo: parseData : data len over limit. It is a abnormal data."),l.ERROR_DATA_FORMAT;const t=255&s[n];n++;const i=new Uint8Array(e-1);0!=i.length?(i.set(s.slice(n,n+i.length)),n+=i.length,this.fillTargetInfo(t,i)):a("ResponseTargetInfo: parseData : type = "+t+", data is empty! Skip it.")}while(n+2<=r);return t+n}fillTargetInfo(e,t){switch(a("fillTargetInfo: number:"+e+" value: "+i(t)),e){case 16:this.name=String.fromCharCode.apply(null,Array.from(t));break;case 0:{const e=t[0]>>4&15,s=15&t[0];this.protocolVersion="V"+e+"."+s;break}case 1:this.quantity=255&t[0],t.length>2&&(this.volume=255&t[1],this.maxVol=255&t[2]),t.length>3&&(this.supportVolumeSync=1==(1&t[3]));break;case 10:t.length>=6?(this.vid=(255&t[0])<<8|t[1],this.pid=(255&t[2])<<8|t[3],this.uid=(255&t[4])<<8|t[5]):4==t.length&&(this.vid=2,this.uid=(255&t[0])<<8|t[1],this.pid=(255&t[2])<<8|t[3]);break;case 2:if(t.length>=6){const e=new Uint8Array(6);e.set(t.slice(0,e.length)),this.edrAddr=o(e)}t.length>=8&&(this.edrProfile=255&t[6],this.edrStatus=255&t[7]);break;case 3:t.length>1&&(this.platform=t[0],this.license=i(t.slice(1)));break;case 4:if(t.length>=5&&(this.functionMask=t[0]<<24|t[1]<<16|t[2]<<8|t[3],this.curFunction=t[4],t.length>5)){const e=t[5];this.supportOfflineShow=1==(1&e),this.supportUsb=2==(2&e),this.supportSd0=4==(4&e),this.supportSd1=8==(8&e),this.hideNetRadio=16==(16&e)}break;case 5:if(t.length>=2){const e=(255&t[0])<<8|t[1],s="V_"+(e>>12&15)+"."+(e>>8&15)+"."+(e>>4&15)+"."+(15&e);this.versionCode=e,this.versionName=s}break;case 6:this.sdkType=t[0],this.supportVolumeSync||(this.supportVolumeSync=2==this.sdkType||4==this.sdkType);break;case 9:this.mandatoryUpgradeFlag=t[0],t.length>=2&&(this.requestOtaFlag=t[1]),t.length>=3&&(this.expandMode=t[2]);break;case 7:if(2==t.length){const e=(255&t[0])<<8|t[1],s="V_"+(e>>12&15)+"."+(e>>8&15)+"."+(e>>4&15)+"."+(15&e);this.ubootVersionCode=e,this.ubootVersionName=s}break;case 8:this.isSupportDoubleBackup=1==(255&t[0]),t.length>=2&&(this.isNeedBootLoader=1==(255&t[1])),t.length>=3&&(this.singleBackupOtaWay=t[2]);break;case 11:this.authKey=String.fromCharCode.apply(null,Array.from(t));break;case 12:this.projectCode=String.fromCharCode.apply(null,Array.from(t));break;case 13:t.length>=4?(this.sendMtu=(255&t[0])<<8|t[1],this.receiveMtu=(255&t[2])<<8|t[3]):2==t.length&&(this.sendMtu=(255&t[0])<<8|t[1],this.receiveMtu=this.sendMtu);break;case 14:this.allowConnectFlag=t[0];break;case 31:this.customVersionMsg=i(t);break;case 17:if(this.bleOnly=1==t[0],t.length>6){const e=new Uint8Array(6);e.set(t.slice(1,1+e.length)),this.bleAddr=o(e)}break;case 18:this.emitterStatus=t[0]>>4&15,this.emitterSupport=1==(15&t[0]);break;case 19:{const e=t[0];this.isSupportMD5=1==(1&e),this.isGameMode=1==(e>>1&1),this.isSupportSearchDevice=1==(e>>2&1),this.supportSoundCard=1==(e>>3&1),this.banEq=1==(e>>4&1),this.supportExternalFlashTransfer=1==(e>>5&1),this.supportAnc=1==(e>>6&1);break}case 20:break;case 21:t.length>=4&&(this.supportPackageCrc16=1==(1&t[0]),this.getFileByNameWithDev=2==(2&t[0]),this.contactsTransferBySmallFile=4==(4&t[0]))}}}class F extends d{constructor(e){super(m.CMD_SETTINGS_COMMUNICATION_MTU,e,new G)}}class V extends p{constructor(){super(...arguments),this.protocolMtu=0}parseData(e){let t=super.parseData(e);return t<l.ERROR_NONE?t:e.length<t+2?l.ERROR_DATA_FORMAT:(this.protocolMtu=(255&e[1])<<8|e[0],t+=2,t)}toData(){const e=new Uint8Array;return e.set(super.toData()),e}}class G extends R{constructor(){super(...arguments),this.realProtocolMtu=0}parseData(e){let t=super.parseData(e);return t<l.ERROR_NONE?t:e.length<t+2?l.ERROR_DATA_FORMAT:(this.realProtocolMtu=(255&e[1])<<8|e[0],t+=2,t)}toData(){const e=new Uint8Array;return e.set(super.toData()),e}}class H extends d{constructor(e){super(m.CMD_EXTRA_CUSTOM,e,new R)}}class B extends d{constructor(e){super(m.CMD_CUSTOM,e,new R)}}class W{}W.CMD_OTA_GET_DEVICE_UPDATE_FILE_INFO_OFFSET=225,W.CMD_OTA_INQUIRE_DEVICE_IF_CAN_UPDATE=226,W.CMD_OTA_ENTER_UPDATE_MODE=227,W.CMD_OTA_EXIT_UPDATE_MODE=228,W.CMD_OTA_SEND_FIRMWARE_UPDATE_BLOCK=229,W.CMD_OTA_GET_DEVICE_REFRESH_FIRMWARE_STATUS=230,W.CMD_REBOOT_DEVICE=231,W.CMD_OTA_NOTIFY_UPDATE_CONTENT_SIZE=232;class Y{constructor(){this.cmdParserMap=new Map,this.cmdParserMap.set(W.CMD_OTA_EXIT_UPDATE_MODE,new Q),this.cmdParserMap.set(W.CMD_OTA_SEND_FIRMWARE_UPDATE_BLOCK,new q),this.cmdParserMap.set(W.CMD_OTA_NOTIFY_UPDATE_CONTENT_SIZE,new K)}getCmdParserMap(){return this.cmdParserMap}}class Q extends h{createCommand(){return new X}}class q extends h{createCommand(){return new $(new ee)}}class K extends h{createCommand(){return new j(new J)}}class z extends R{constructor(){super(...arguments),this.result=_.RESULT_OK}parseData(e){let t=super.parseData(e);if(t<l.ERROR_NONE)return t;const s=e.length-t,n=new Uint8Array(s);return n.set(e.slice(t,t+n.length)),t+=n.length,this.result=n[0],t}toData(){const e=new Uint8Array;let t=0;const s=super.toData();return e.set(s,t),t+=s.length,e[t]=this.result,t+=1,e}}class X extends d{constructor(){super(W.CMD_OTA_EXIT_UPDATE_MODE,new p,new _)}}class j extends d{constructor(e){super(W.CMD_OTA_NOTIFY_UPDATE_CONTENT_SIZE,e,new R)}}class J extends p{constructor(){super(...arguments),this.totalSize=0,this.currentSize=0}parseData(e){let t=super.parseData(e);return t<l.ERROR_NONE?t:e.length<t+4?l.ERROR_DATA_FORMAT:(this.totalSize=e[t+0]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3],t+=4,e.length>=t+4&&(this.currentSize=e[t+0]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3],t+=4),t)}toData(){const e=super.toData();let t;t=this.currentSize>0?e.length+8:e.length+4;const s=new Uint8Array(t);let n=0;return s.set(e,n),n+=e.length,s[n]=this.totalSize>>24,s[n+1]=this.totalSize>>16&255,s[n+2]=this.totalSize>>8&255,s[n+3]=255&this.totalSize,n+=4,this.currentSize>0&&(s[n]=this.totalSize>>24,s[n+1]=this.totalSize>>16&255,s[n+2]=this.totalSize>>8&255,s[n+3]=255&this.totalSize,n+=4),s}}class Z extends d{constructor(){super(W.CMD_OTA_GET_DEVICE_REFRESH_FIRMWARE_STATUS,new p,new _)}}Z.UPGRADE_RESULT_COMPLETE=0,Z.UPGRADE_RESULT_DATA_CHECK_ERROR=1,Z.UPGRADE_RESULT_FAIL=2,Z.UPGRADE_RESULT_ENCRYPTED_KEY_NOT_MATCH=3,Z.UPGRADE_RESULT_UPGRADE_FILE_ERROR=4,Z.UPGRADE_RESULT_UPGRADE_TYPE_ERROR=5,Z.UPGRADE_RESULT_ERROR_LENGTH=6,Z.UPGRADE_RESULT_FLASH_READ=7,Z.UPGRADE_RESULT_CMD_TIMEOUT=8,Z.UPGRADE_RESULT_DOWNLOAD_BOOT_LOADER_SUCCESS=128;class $ extends d{constructor(e){super(W.CMD_OTA_SEND_FIRMWARE_UPDATE_BLOCK,e,new te)}}class ee extends p{constructor(){super(...arguments),this.offset=0,this.len=0}toData(){const e=super.toData(),t=new Uint8Array(e.length+6);let s=0;return t.set(e,s),s+=e.length,t[s]=this.offset>>24,t[s+1]=this.offset>>16&255,t[s+2]=this.offset>>8&255,t[s+3]=255&this.offset,s+=4,t[s]=this.len>>8&255,t[s+1]=255&this.len,s+=2,t}parseData(e){let t=super.parseData(e);return t<l.ERROR_NONE?t:e.length<t+4+2?l.ERROR_DATA_FORMAT:(this.offset=e[t+0]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3],t+=4,this.len=e[t+0]<<8|e[t+1],t+=2,t)}}class te extends R{toData(){const e=super.toData();if(null!=this.block&&this.block.length>0){const t=new Uint8Array(e.length+this.block.length);let s=0;return t.set(e,s),s+=e.length,t.set(this.block,s),s+=this.block.length,t}return e}parseData(e){let t=super.parseData(e);if(t<l.ERROR_NONE)return t;const s=e.length-t;if(0==s)return this.block=new Uint8Array,this.block[0]=0,t;const n=new Uint8Array(s);return n.set(e.slice(t,t+n.length)),t+=n.length,this.block=n,t}}class se extends R{constructor(){super(...arguments),this.offset=0,this.len=0}toData(){const e=super.toData(),t=new Uint8Array(e.length+6);let s=0;return t.set(e,s),s+=e.length,t[s]=this.offset>>24,t[s+1]=this.offset>>16&255,t[s+2]=this.offset>>8&255,t[s+3]=255&this.offset,s+=4,t[s]=this.len>>8&255,t[s+1]=255&this.len,s+=2,t}parseData(e){let t=super.parseData(e);return t<l.ERROR_NONE?t:e.length<t+4+2?l.ERROR_DATA_FORMAT:(this.offset=e[t+0]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3],t+=4,this.len=e[t+0]<<8|e[t+1],t+=2,t)}}class ne extends p{constructor(e){super(),this.op=e}toData(){const e=super.toData(),t=new Uint8Array(e.length+1);let s=0;return t.set(e,s),s+=e.length,t[s]=255&this.op,s+=1,t}}ne.OP_REBOOT=0,ne.OP_CLOSE=1;class ae extends d{constructor(e){super(W.CMD_OTA_INQUIRE_DEVICE_IF_CAN_UPDATE,e,new _)}}ae.RESULT_CAN_UPDATE=0,ae.RESULT_DEVICE_LOW_VOLTAGE_EQUIPMENT=1,ae.RESULT_FIRMWARE_INFO_ERROR=2,ae.RESULT_FIRMWARE_VERSION_NO_CHANGE=3,ae.RESULT_TWS_NOT_CONNECT=4,ae.RESULT_HEADSET_NOT_IN_CHARGING_BIN=5;class re{}re.CMD_ADV_SETTINGS=192,re.CMD_ADV_GET_INFO=193,re.CMD_ADV_DEVICE_NOTIFY=194,re.CMD_ADV_NOTIFY_SETTINGS=195,re.CMD_ADV_DEV_REQUEST_OPERATION=196;class ie{constructor(){this.cmdParserMap=new Map,this.cmdParserMap.set(re.CMD_ADV_DEVICE_NOTIFY,new ce),this.cmdParserMap.set(re.CMD_ADV_DEV_REQUEST_OPERATION,new oe)}getCmdParserMap(){return this.cmdParserMap}}class oe extends h{createCommand(){return new ue}}class ce extends h{createCommand(){return new de}}class le extends p{constructor(e){super(),this.op=-1,null!=e&&(this.op=e)}toData(){const e=super.toData(),t=new Uint8Array(e.length+1);let s=0;return t.set(e,s),s+=e.length,t[s]=this.op,t}parseData(e){let t=super.parseData(e);return t<l.ERROR_NONE?t:e.length<t+1?l.ERROR_DATA_FORMAT:(this.op=255&e[t],t++,t)}}class he extends d{constructor(e){super(re.CMD_ADV_NOTIFY_SETTINGS,new le(e),new _)}}he.CTRL_OP_CLOSE=0,he.CTRL_OP_OPEN=1;class ue extends d{constructor(){super(re.CMD_ADV_DEV_REQUEST_OPERATION,new le,new R)}}ue.REQUEST_OP_SYNC_SETTINGS=0,ue.REQUEST_OP_UPDATE_SETTINGS_AND_REBOOT=1,ue.REQUEST_OP_SYNC_CONNECTION_TIME=2,ue.REQUEST_OP_RECONNECT_DEVICE=3,ue.REQUEST_OP_SYNC_DEVICE_INFO=4;class de extends d{constructor(){super(re.CMD_ADV_DEVICE_NOTIFY,new pe,null)}}class pe extends p{constructor(){super(...arguments),this.vid=0,this.pid=0,this.uid=0,this.deviceType=0,this.version=0,this.showDialog=!1,this.edrAddr="",this.seq=0,this.action=0,this.leftDeviceQuantity=0,this.leftCharging=!1,this.rightDeviceQuantity=0,this.rightCharging=!1,this.chargingBinQuantity=0,this.deviceCharging=!1}getVid(){return this.vid}setVid(e){return this.vid=e,this}getPid(){return this.pid}setPid(e){return this.pid=e,this}getUid(){return this.uid}setUid(e){return this.uid=e,this}getDeviceType(){return this.deviceType}setDeviceType(e){return this.deviceType=e,this}getVersion(){return this.version}setVersion(e){return this.version=e,this}isShowDialog(){return this.showDialog}setShowDialog(e){return this.showDialog=e,this}getEdrAddr(){return this.edrAddr}setEdrAddr(e){return this.edrAddr=e,this}getSeq(){return this.seq}setSeq(e){return this.seq=e,this}getAction(){return this.action}setAction(e){return this.action=e,this}getLeftDeviceQuantity(){return this.leftDeviceQuantity}setLeftDeviceQuantity(e){return this.leftDeviceQuantity=e,this}isLeftCharging(){return this.leftCharging}setLeftCharging(e){return this.leftCharging=e,this}getRightDeviceQuantity(){return this.rightDeviceQuantity}setRightDeviceQuantity(e){return this.rightDeviceQuantity=e,this}isRightCharging(){return this.rightCharging}setRightCharging(e){return this.rightCharging=e,this}getChargingBinQuantity(){return this.chargingBinQuantity}setChargingBinQuantity(e){return this.chargingBinQuantity=e,this}isDeviceCharging(){return this.deviceCharging}setDeviceCharging(e){return this.deviceCharging=e,this}parseData(e){let t=super.parseData(e);if(t<l.ERROR_NONE)return t;if(e.length<t+18)return l.ERROR_DATA_FORMAT;const s=new Uint8Array(18);return s.set(e.slice(t,t+18)),t+=18,this.fillADVInfo(s),t}fillADVInfo(e){this.setVid(e[0]<<8+e[1]).setUid(e[2]<<8+e[3]).setPid(e[4]<<8+e[5]),this.setDeviceType(e[6]>>4&255).setVersion(15&e[6]);const t=new Uint8Array(6);t.set(e.slice(7,7+t.length)),this.setEdrAddr(o(t)).setAction(e[13]),this.setLeftCharging(1==(e[14]>>7&1)).setLeftDeviceQuantity(127&e[14]),this.setRightCharging(1==(e[15]>>7&1)).setRightDeviceQuantity(127&e[15]),this.setDeviceCharging(1==(e[16]>>7&1)).setChargingBinQuantity(127&e[16]),this.setSeq(e[17])}}class Re extends class{}{constructor(){super(...arguments),this.deviceInfoMap=new Map}release(){this.deviceInfoMap.clear()}getReceiveMtu(e){const t=this.getDeviceInfo(e);return null!=t&&t.receiveMtu>0?t.receiveMtu:null}getSendMtu(e){const t=this.getDeviceInfo(e);return null!=t&&t.sendMtu>0?t.sendMtu:null}getDeviceInfo(e){return this.deviceInfoMap.get(e.mac)}removeDeviceInfo(e){if(0!=this.deviceInfoMap.size)return this.deviceInfoMap.delete(e.mac)}updateDeviceInfo(e,t){this.deviceInfoMap.set(e.mac,t)}}class _e{onRcspInit(e,t){}onRcspCommand(e,t){}onRcspDataCmd(e,t){}onRcspError(e,t,s){}onMandatoryUpgrade(e){}onConnectStateChange(e,t){}}class De{constructor(){this.cmdSn=0,this.cmdSnMap=new Map,this.cmdSn=parseInt(256*Math.random()+"")}getRcspCmdSeq(e){return this.autoIncSN(e)}autoIncSN(e){const t=this.getCmdSn(e),s=(t+1)%256;return null==e?this.cmdSn=s:this.cmdSnMap.set(e.mac,s),t}resetCmdSeq(e){null==e?this.cmdSn=0:this.cmdSnMap.delete(e.mac)}release(){this.cmdSn=0,this.cmdSnMap.clear()}getCmdSn(e){if(null==e)return this.cmdSn;let t=this.cmdSnMap.get(e.mac);return null==t?this.cmdSn:t}}class Ee extends _e{constructor(){super(...arguments),this.mCallbacks=new Array}registerRcspCallback(e){null!=e&&this.mCallbacks.push(e)}unregisterRcspCallback(e){if(null!=e){var t=this.mCallbacks.indexOf(e);-1!=t&&this.mCallbacks.splice(t,1)}}release(){this.mCallbacks.length=0}onRcspInit(e,t){this.callbackRcspEvent({onCallback:s=>{s.onRcspInit(e,t)}})}onRcspCommand(e,t){this.callbackRcspEvent({onCallback:s=>{s.onRcspCommand(e,t)}})}onRcspDataCmd(e,t){this.callbackRcspEvent({onCallback:s=>{s.onRcspDataCmd(e,t)}})}onRcspError(e,t,s){this.callbackRcspEvent({onCallback:n=>{n.onRcspError(e,t,s)}})}onMandatoryUpgrade(e){this.callbackRcspEvent({onCallback:t=>{t.onMandatoryUpgrade(e)}})}onConnectStateChange(e,t){this.callbackRcspEvent({onCallback:s=>{s.onConnectStateChange(e,t)}})}callbackRcspEvent(e){0!=this.mCallbacks.length&&this.mCallbacks.forEach((t=>{e.onCallback(t)}))}}exports.CmdChangeCommunicationWay=class extends d{constructor(e){super(m.CMD_SWITCH_DEVICE_REQUEST,e,new _)}},exports.CmdControlADVStream=he,exports.CmdCustom=H,exports.CmdData=P,exports.CmdDeviceRequest=ue,exports.CmdEnterUpdateMode=class extends d{constructor(){super(W.CMD_OTA_ENTER_UPDATE_MODE,new p,new z)}},exports.CmdExitUpdateMode=X,exports.CmdGetSysInfo=class extends d{constructor(e){super(m.CMD_GET_SYS_INFO,e,new k)}},exports.CmdGetTargetInfo=w,exports.CmdInnerCustom=B,exports.CmdNotifyADVInfo=de,exports.CmdNotifySysInfo=y,exports.CmdNotifyUpdateFileSize=j,exports.CmdOpCodeBase=m,exports.CmdOpCodeHeadSet=re,exports.CmdOpCodeOta=W,exports.CmdQueryUpdateResult=Z,exports.CmdReadFileBlock=$,exports.CmdReadFileOffset=class extends d{constructor(){super(W.CMD_OTA_GET_DEVICE_UPDATE_FILE_INFO_OFFSET,new p,new se)}},exports.CmdRebootDevice=class extends d{constructor(e){super(W.CMD_REBOOT_DEVICE,e,new _)}},exports.CmdRequestUpdate=ae,exports.CmdSetMtu=F,exports.CmdSetSysInfo=class extends d{constructor(e){super(m.CMD_SET_SYS_INFO,e,new R)}},exports.Command=d,exports.CommandBase=class extends d{},exports.Device=class{constructor(e){this.mac=e}equals(e){return null!=e&&(this==e||this.mac==e.mac)}},exports.DeviceInfo=class extends L{},exports.DeviceInfoManager=Re,exports.ErrorCode=l,exports.OnRcspCallback=_e,exports.ParamADVInfo=pe,exports.ParamBase=p,exports.ParamCommunicationWay=class extends p{constructor(e,t){super(),this.isSupportNewRebootWay=!1,this.communicationWay=e,t&&(this.isSupportNewRebootWay=t)}toData(){const e=super.toData(),t=new Uint8Array(e.length+2);let s=0;return t.set(e,s),s+=e.length,t[s]=this.communicationWay,t[s+1]=this.isSupportNewRebootWay?1:0,t}},exports.ParamData=v,exports.ParamFileBlock=ee,exports.ParamGetSysInfo=class extends p{constructor(){super(...arguments),this.function=0,this.mask=0}toData(){const e=new Uint8Array;let t=0;const s=super.toData();e.set(s),t+=s.length,e[t]=null==this.function?0:this.function,t++;const n=null==this.mask?0:this.mask;return e[t]=n>>24,e[t+1]=n>>16&255,e[t+2]=n>>8&255,e[t+3]=255&n,e}},exports.ParamMtu=V,exports.ParamOperation=le,exports.ParamRebootDevice=ne,exports.ParamRequestUpdate=class extends p{constructor(e){super(),this.data=e}toData(){const e=super.toData();if(null!=this.data&&this.data.length>0){const t=new Uint8Array(e.length+this.data.length);let s=0;return t.set(e,s),s+=e.length,t.set(this.data,s),s+=1,t}return e}},exports.ParamSysInfo=b,exports.ParamTargetInfo=x,exports.ParamUpdateFileSize=J,exports.RcspConstant=c,exports.RcspOpImpl=class{constructor(){this.mTargetDevice=null,this.mCmdSnGenerator=new De,this.mRcspCallbackManager=new Ee,this.mDeviceInfoManager=new Re;const e={onRcspCommand:(e,t)=>{this.dealWithRcspCommand(e,t)},onRcspResponse:(e,t)=>{this.dealWithRcspResponse(e,t)},onError:(e,t,s)=>{this.mRcspCallbackManager.onRcspError(e,t,s)}};this.mRCSPDataHandler=new T(this,this.mDeviceInfoManager,function(){const e=new Map,t=(new A).getCmdParserMap();for(const s of t)e.set(s[0],s[1]);const s=(new Y).getCmdParserMap();for(const t of s)e.set(t[0],t[1]);const n=(new ie).getCmdParserMap();for(const t of n)e.set(t[0],t[1]);return e}(),e)}getUsingDevice(){return this.mTargetDevice}setOnSendDataCallback(e){this.mOnSendDataCallback=e}isDeviceConnected(){return null!=this.getUsingDevice()&&null!=this.getDeviceInfo(this.getUsingDevice())}getDeviceInfo(e){return this.mDeviceInfoManager.getDeviceInfo(e)}addOnRcspCallback(e){this.mRcspCallbackManager.registerRcspCallback(e)}removeOnRcspCallback(e){this.mRcspCallbackManager.unregisterRcspCallback(e)}transmitDeviceStatus(e,t){this.mRcspCallbackManager.onConnectStateChange(e,t),this.handleDeviceStatus(e,t)}transmitDeviceData(e,t){this.handleDeviceReceiveData(e,t)}sendDataToDevice(e,t){return null==this.mOnSendDataCallback?(r("RcspImpl: OnSendDataCallback is null,so sendDataToDevice failed"),!1):this.mOnSendDataCallback.sendDataToDevice(e,t)}sendRCSPCommand(e,t,s,n){if(!this.checkIsValidDevice(e,n))return;if(null==t){const t=l.ERROR_INVALID_PARAM;return void this.callbackCmdError(n,e,t,l.getErrorDesc2(t,"Command is null."))}t.isCommand()&&t.setSn(this.mCmdSnGenerator.getRcspCmdSeq(e));const a=new E(e,t,s,n);this.mRCSPDataHandler.addSendData(a)}syncDeviceInfo(e,t,s){const n=new w(t);this.sendCommand(e,n,{onCmdResponse(e,t){if(t.getStatus()!=R.STATUS_SUCCESS){const s=l.ERROR_REPLY_BAD_STATUS;return void this.onError(e,s,l.getErrorDesc2(s,""+t.getStatus()))}const n=t.getResponse();if(null!=n)a("RcspOpImpl: syncDeviceInfo : "+t),null!=s&&s.onResult(e,n);else{const t=l.ERROR_DATA_FORMAT;this.onError(e,t,l.getErrorDesc2(t,"Get targetInfo is null."))}},onError(e,t,n){null!=s&&s.onError(e,t,n)}})}destroy(){this.mCmdSnGenerator.release(),this.mRcspCallbackManager.release(),this.mTargetDevice=null,this.mRCSPDataHandler.destroy()}getDeviceInfoManager(){return this.mDeviceInfoManager}callbackCmdError(e,t,s,n){null!=e&&e.onError(t,s,l.getErrorDesc2(s,n))}callbackError(e,t,s){const n=l.getErrorDesc2(t,s);r("RcspOpImpl: callbackError : device["+e+"] has an exception: "+t+", "+n),this.mRCSPDataHandler.listener.onError(e,t,n)}sendCommand(e,t,s){this.sendRCSPCommand(e,t,c.DEFAULT_SEND_CMD_TIMEOUT,s)}handleDeviceStatus(e,t){if(null!=t)switch(t){case exports.Connection.CONNECTION_DISCONNECT:e.equals(this.mTargetDevice)&&(this.mTargetDevice=null,this.getDeviceInfoManager().removeDeviceInfo(e),this.mRCSPDataHandler.stopHandler());break;case exports.Connection.CONNECTION_CONNECTED:if(null!=this.mTargetDevice){const t="Device is Connected. device : "+e+", connected Device : "+this.mTargetDevice;return a("RcspOpImpl: "+t),void this.callbackError(e,l.ERROR_REPEAT_STATUS,t)}this.mTargetDevice=e,this.mRCSPDataHandler.startHandler(),this.handleDeviceConnectedEvent(e)}}handleDeviceConnectedEvent(e){const t=this.getDeviceInfo(e);if(a("RcspOpImpl: handleDeviceConnectedEvent : "+JSON.stringify(t)),null==t){const t=new x(4294967295,2),s=this;this.syncDeviceInfo(e,t,{onResult(e,t){s.getDeviceInfoManager().updateDeviceInfo(e,t),a("RcspOpImpl: handleDeviceConnectedEvent : onResult ===> "+JSON.stringify(t)),s.handleDeviceConnectedEvent(e)},onError(e,t,n){s.mRcspCallbackManager.onRcspInit(e,!1),r("RcspOpImpl:  Init RCSP protocol failed. so callback the device is disconnected.\ncode = "+t+", "+n),s.handleDeviceStatus(e,exports.Connection.CONNECTION_DISCONNECT)}})}else t.mandatoryUpgradeFlag?(t.receiveMtu<c.DEFAULT_PROTOCOL_MTU&&(t.receiveMtu=c.DEFAULT_PROTOCOL_MTU),this.getDeviceInfoManager().updateDeviceInfo(e,t),a("RcspOpImpl: handleDeviceConnectedEvent : device["+JSON.stringify(e)+"] need update."),this.mRcspCallbackManager.onRcspInit(e,!0),this.mRcspCallbackManager.onMandatoryUpgrade(e)):(a("RcspOpImpl: handleDeviceConnectedEvent : init success."),this.mRcspCallbackManager.onRcspInit(e,!0))}checkIsValidDevice(e,t){return!!e.equals(this.mTargetDevice)||(null!=t?this.callbackCmdError(t,e,l.ERROR_DEVICE_OFFLINE,""):this.callbackError(e,l.ERROR_DEVICE_OFFLINE,""),!1)}handleDeviceReceiveData(e,t){null!=e&&null!=t&&0!=t.length&&this.checkIsValidDevice(e,null)&&this.mRCSPDataHandler.addReceiveData(new C(e,t))}dealWithRcspCommand(e,t){if(this.checkIsValidDevice(e,null))if(t.getOpCode()==m.CMD_DATA)this.mRcspCallbackManager.onRcspDataCmd(e,t);else switch(this.mRcspCallbackManager.onRcspCommand(e,t),t.getOpCode()){case m.CMD_DATA:this.mRcspCallbackManager.onRcspDataCmd(e,t);break;case m.CMD_SETTINGS_COMMUNICATION_MTU:const s=t,n=s.getParam().protocolMtu,a=this.getDeviceInfo(e);null!=a&&(a.receiveMtu=n,this.getDeviceInfoManager().updateDeviceInfo(e,a));const r=s.getResponse();null!=r&&(r.setSn(t.getSn()),r.setStatus(R.STATUS_SUCCESS),r.realProtocolMtu=n),s.setCommand(!1),this.sendCommand(e,s,null);case m.CMD_SYS_INFO_AUTO_UPDATE:}}dealWithRcspResponse(e,t){if(this.checkIsValidDevice(e,null)&&t.getStatus()==R.STATUS_SUCCESS&&t.getOpCode()===m.CMD_SETTINGS_COMMUNICATION_MTU){const s=t.getResponse().realProtocolMtu,n=this.getDeviceInfo(e);null!=n&&(n.receiveMtu=s,this.getDeviceInfoManager().updateDeviceInfo(e,n))}}},exports.ResponseBase=R,exports.ResponseData=class extends R{toData(){const e=new Uint8Array;let t=0;const s=super.toData();return e.set(s,t),t+=s.length,null!=this.responseOpCode&&(e[t]=this.responseOpCode,t+=1),e}parseData(e){let t=super.parseData(e);return t<l.ERROR_NONE?t:e.length<t+1?l.ERROR_DATA_FORMAT:(this.responseOpCode=255&e[t],t++,t)}},exports.ResponseFileBlock=te,exports.ResponseFileOffset=se,exports.ResponseMtu=G,exports.ResponseResult=_,exports.ResponseSysInfo=k,exports.ResponseTargetInfo=L,exports.setLogGrade=function(e){n=e};
//# sourceMappingURL=jl_rcsp_ota_2.0.0.js.map
