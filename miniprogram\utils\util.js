const sign = require('../utils/sign.js');
const gbk = require('./gbk.js');
console.log("sasas" + gbk);

const formatTime = date => {
  var date = new Date(date)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':')
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : '0' + n
}
//获取当前时间戳
function GetTimestamp() {
  var timestamp = Date.parse(new Date());
  return timestamp;
}

//存储令牌
const setToken = token => {
  wx.setStorageSync('AccessToken', token.AccessToken);
  wx.setStorageSync('AExpires', token.AccessTokenUtcExpires);
  wx.setStorageSync('RefreshToken', token.RefreshToken);
  wx.setStorageSync('RExpires', token.RefreshUtcExpires);
}
//验证token是否过期
const checkToken = async token => {
  await new Promise(async (resolve, reject) => {
    var RExpires = wx.getStorageSync('RExpires');
    var AExpires = wx.getStorageSync('AExpires');
    var TimeNow = GetTimestamp();
    if (RExpires < TimeNow) {
      resolve(false);
    } else if (AExpires < TimeNow) {
      await RefreshToken().then(value => {
        resolve(value);
      });
    } else {
      resolve(true);
    }
  })
}
//刷新令牌
const RefreshToken = () => {
  return new Promise((resolve, reject) => {
    var RToken = wx.getStorageSync('RefreshToken');
    var json = {};
    json["content-type"] = 'application/x-www-form-urlencoded';
    json["Cookie"] = wx.getStorageSync("sessionid");
    json = sign.sign(json, getApp().globalData.server_token);
    wx.request({
      url: getApp().globalData.apiUrl + "V1/Account/RefreshToken",
      data: {
        RefreshToken: RToken,
        Lng: getApp().lng
      },
      header: json,
      method: 'POST',
      success: (res) => {
        if (res.statusCode == 200 && res.data.Code == 1) {
          wx.removeStorageSync('AccessToken');
          wx.removeStorageSync('AExpires');
          wx.removeStorageSync('RefreshToken');
          wx.removeStorageSync('RExpires');
          setToken(res.data.Data.Token);
          resolve(true);
        } else {
          resolve(false);
        }
      }
    })
  })
}
//验证手机号
const checkPhone = (mobile) => {
  return /^1[34578]\d{9}$/.test(mobile);
}
//POST请求
const requestPost = (url, data, callback) => {
  var AExpires = wx.getStorageSync('AExpires');
  var RExpires = wx.getStorageSync('RExpires');
  if (RExpires < GetTimestamp()) {
    wx.setStorage({
      data: false,
      key: 'dhmobile',
    });

    setTimeout(function () {
      wx.navigateTo({
        url: '/pages/index/index',
      })
    }, 1000)

    return;
  } else if (AExpires < GetTimestamp()) {
    RefreshToken().then((value) => {
      if (value) {
        Post(url, data, callback);
      } else {
        wx.setStorage({
          data: false,
          key: 'dhmobile',
        });

        setTimeout(function () {
          wx.navigateTo({
            url: '/pages/index/index',
          })
        }, 1000)
        return;
      }
    });
  } else {
    Post(url, data, callback);
  }
}

const Post = (url, data, callback) => {
  var AccessToken = wx.getStorageSync('AccessToken');
  var json = {};
  json["Content-type"] = 'application/x-www-form-urlencoded';
  json["Authorization"] = 'Bearer ' + AccessToken;
  json["Cookie"] = wx.getStorageSync("sessionid");
  json = sign.sign(json, getApp().globalData.server_token);
  wx.request({
    url: getApp().globalData.apiUrl + url,
    data: data,
    header: json,
    method: 'POST',
    success: async (result) => {
      if (result.statusCode == 401 || result.statusCode == 403) {
        var RExpires = wx.getStorageSync('RExpires');
        var TimeNow = GetTimestamp();
        if (RExpires < TimeNow) {
          wx.setStorage({
            data: false,
            key: 'dhmobile',
          });

          setTimeout(function () {
            wx.navigateTo({
              url: '/pages/index/index',
            })
          }, 1000)
          return;
        } else {
          RefreshToken().then(value => {
            if (value) {
              requestPost(url, data, callback);
            } else {
              wx.setStorage({
                data: false,
                key: 'dhmobile',
              });

              setTimeout(function () {
                wx.navigateTo({
                  url: '/pages/index/index',
                })
              }, 1000)
              return;
            }
          });
          return;
        }
      } else {
        callback(result);
      }
    },
  })
}
const uploadFild = (url, data, file, filename, callback) => {
  var AExpires = wx.getStorageSync('AExpires');
  var RExpires = wx.getStorageSync('RExpires');
  if (RExpires < GetTimestamp()) {
    wx.setStorage({
      data: false,
      key: 'dhmobile',
    });

    setTimeout(function () {
      wx.navigateTo({
        url: '/pages/index/index',
      })
    }, 1000)
    return;
  } else if (AExpires < GetTimestamp()) {
    RefreshToken().then((value) => {
      if (value) {
        Fild(url, data, file, filename, callback);
      } else {
        wx.setStorage({
          data: false,
          key: 'dhmobile',
        });

        setTimeout(function () {
          wx.navigateTo({
            url: '/pages/index/index',
          })
        }, 1000)
        return;
      }
    });
  } else {
    Fild(url, data, file, filename, callback);
  }
}
//上传文件
const Fild = (url, data, file, filename, callback) => {
  var AccessToken = wx.getStorageSync('AccessToken');
  var json = {};
  json["Content-type"] = 'application/x-www-form-urlencoded';
  json["Authorization"] = 'Bearer ' + AccessToken;
  json["Cookie"] = wx.getStorageSync("sessionid");
  json = sign.sign(json, getApp().globalData.server_token);
  wx.uploadFile({
    filePath: file,
    name: filename,
    url: getApp().globalData.apiUrl + url,
    formData: data,
    header: json,
    success: (res) => {
      console.log(res);
      if (res.statusCode == 401 || res.statusCode == 403) {
        var RExpires = wx.getStorageSync('RExpires');
        var AExpires = wx.getStorageSync('AExpires');
        var TimeNow = GetTimestamp();
        if (RExpires < TimeNow) {
          wx.setStorage({
            data: false,
            key: 'dhmobile',
          });

          setTimeout(function () {
            wx.navigateTo({
              url: '/pages/index/index',
            })
          }, 1000)
          return;
        } else {
          RefreshToken().then(value => {
            if (value) {
              requestPost(url, data, callback);
            } else {
              wx.setStorage({
                data: false,
                key: 'dhmobile',
              });

              setTimeout(function () {
                wx.navigateTo({
                  url: '/pages/index/index',
                })
              }, 1000)
              return;
            }
          });
          return;
        }
      } else {
        if ('Set-Cookie' in res.header) {
          wx.setStorageSync("sessionid", res.header['Set-Cookie']);
        } else if ('set-cookie' in res.header) {
          wx.setStorageSync("sessionid", res.header['set-cookie'])
        }
        callback(res);
      }
    }
  })
}


const requestPostNotToken = (url, data, callback) => {
  var json = {};
  json["content-type"] = 'application/x-www-form-urlencoded';
  json = sign.sign(json, getApp().globalData.server_token);
  json["cookie"] = wx.getStorageSync("sessionid");
  wx.request({
    url: getApp().globalData.apiUrl + url,
    method: "Post",
    data: data,
    header: json,
    success: (result) => {
      if ('Set-Cookie' in result.header) {
        wx.setStorageSync("sessionid", result.header['Set-Cookie']);
      } else if ('set-cookie' in res.header) {
        wx.setStorageSync("sessionid", result.header['set-cookie'])
      }
      callback(result);
    }
  })

}

const GetAddress = (lat, lon, callback) => {
  var getAddressUrl = "https://apis.map.qq.com/ws/geocoder/v1/?location=" + lat + "," + lon + "&key=" + getApp().MapKey;
  wx.request({
    url: getAddressUrl,
    success: function (result) {
      callback(result.data.result.address);
    }
  })
}

//存IotId
const SetIotId = (value) => {
  wx.setStorageSync('IotId', value);
}

//获取IotId
const GetIotId = () => {
  return wx.getStorageSync('IotId');
}

//存储用户数据
const SetUser = async (user) => {
  return new Promise((resolve, reject) => {
    wx.setStorageSync('user', {
      DisplayName: user.DisplayName,
      Mobile: user.Mobile,
      Avatar: user.Avatar == "" ? getApp().DetailsAvatar : user.Avatar,
      Remark: "暂无"
    })
    resolve();
  })
}

//获取用户数据
const GetUser = (user) => {
  return wx.getStorageSync('user');
}

const hexStringToBuff = str => { //str='中国：WXHSH'
  const buffer = new ArrayBuffer((sumStrLength(str)) * 4)
  const dataView = new DataView(buffer)
  var data = str.toString();
  var p = 0; //ArrayBuffer 偏移量
  for (var i = 0; i < data.length; i++) {
    if (isCN(data[i])) { //是中文
      //调用GBK 转码
      var t = gbk.encode(data[i]);
      for (var j = 0; j < 2; j++) {
        //var code = t[j * 2] + t[j * 2 + 1];
        var code = t[j * 3 + 1] + t[j * 3 + 2];
        var temp = parseInt(code, 16)
        //var temp = strToHexCharCode(code);
        dataView.setUint8(p++, temp)
      }
    } else {
      var temp = data.charCodeAt(i);
      dataView.setUint8(p++, temp)
    }
  }
  return buffer;
}

function toUnicode(s) {
  var str = "";
  for (var i = 0; i < s.length; i++) {
    str += "\\u" + s.charCodeAt(i).toString(16) + "\t";
  }
  return str;
}

function strToHexCharCode(str) {
  if (str === "")
    return "";
  var hexCharCode = [];
  hexCharCode.push("0x");
  for (var i = 0; i < str.length; i++) {
    hexCharCode.push((str.charCodeAt(i)).toString(16));
  }
  return hexCharCode.join("");
}

function sumStrLength(str) {
  var length = 0;
  var data = str.toString();
  for (var i = 0; i < data.length; i++) {
    if (isCN(data[i])) { //是中文
      length += 2;
    } else {
      length += 1;
    }
  }
  return length;
}

function isCN(str) {
  if (/^[\u3220-\uFA29]+$/.test(str)) {
    return true;
  } else {
    return false;
  }
}

//汉字转码
function hexStringToArrayBuffer(str) {
  const buffer = new ArrayBuffer((str.length / 2) + 1)
  const dataView = new DataView(buffer)
  for (var i = 0; i < str.length / 2; i++) {
    var temp = parseInt(str[i * 2] + str[i * 2 + 1], 16)
    dataView.setUint8(i, temp)
  }
  dataView.setUint8((str.length / 2), 0x0a)
  return buffer;
}

//返回八位数组
function subString(str) {
  var arr = [];
  if (str.length > 8) { //大于8
    for (var i = 0;
      (i * 8) < str.length; i++) {
      var temp = str.substring(i * 8, 8 * i + 8);
      arr.push(temp)
    }
    return arr;
  } else {
    return str
  }
}

//不带有汉字
function hexStringToArrayBufferstr(str) {
  let val = ""
  for (let i = 0; i < str.length; i++) {
    if (val === '') {
      val = str.charCodeAt(i).toString(16)
    } else {
      val += ',' + str.charCodeAt(i).toString(16)
    }
  }
  val += "," + "0x0a";
  console.log(val)
  // 将16进制转化为ArrayBuffer
  return new Uint8Array(val.match(/[\da-f]{2}/gi).map(function (h) {
    return parseInt(h, 16)
  })).buffer
}

function send0X0A() {
  const buffer = new ArrayBuffer(1)
  const dataView = new DataView(buffer)
  dataView.setUint8(0, 0x0a)
  return buffer;
}

function stringToArrayBuffer(str) {
  var bytes = new Array();
  var len, c;
  len = str.length;
  for (var i = 0; i < len; i++) {
    c = str.charCodeAt(i);
    if (c >= 0x010000 && c <= 0x10FFFF) {
      bytes.push(((c >> 18) & 0x07) | 0xF0);
      bytes.push(((c >> 12) & 0x3F) | 0x80);
      bytes.push(((c >> 6) & 0x3F) | 0x80);
      bytes.push((c & 0x3F) | 0x80);
    } else if (c >= 0x000800 && c <= 0x00FFFF) {
      bytes.push(((c >> 12) & 0x0F) | 0xE0);
      bytes.push(((c >> 6) & 0x3F) | 0x80);
      bytes.push((c & 0x3F) | 0x80);
    } else if (c >= 0x000080 && c <= 0x0007FF) {
      bytes.push(((c >> 6) & 0x1F) | 0xC0);
      bytes.push((c & 0x3F) | 0x80);
    } else {
      bytes.push(c & 0xFF);
    }
  }
  var array = new Int8Array(bytes.length);
  for (var i in bytes) {
    array[i] = bytes[i];
  }
  return array.buffer;
}

function arrayBufferToString(arr) {
  if (typeof arr === 'string') {
    return arr;
  }
  var dataview = new DataView(arr.data);
  var ints = new Uint8Array(arr.data.byteLength);
  for (var i = 0; i < ints.length; i++) {
    ints[i] = dataview.getUint8(i);
  }
  arr = ints;
  var str = '',
    _arr = arr;
  for (var i = 0; i < _arr.length; i++) {
    var one = _arr[i].toString(2),
      v = one.match(/^1+?(?=0)/);
    if (v && one.length == 8) {
      var bytesLength = v[0].length;
      var store = _arr[i].toString(2).slice(7 - bytesLength);
      for (var st = 1; st < bytesLength; st++) {
        store += _arr[st + i].toString(2).slice(2);
      }
      str += String.fromCharCode(parseInt(store, 2));
      i += bytesLength - 1;
    } else {
      str += String.fromCharCode(_arr[i]);
    }
  }
  return str;
}

function buf2hex(buffer) {
  return Array.prototype.map.call(new Uint8Array(buffer), x => ('00' + x.toString(16)).slice(-2)).join('');
}

module.exports = {
  formatTime: formatTime,
  setToken: setToken,
  checkPhone: checkPhone,
  checkToken: checkToken,
  Post: requestPost,
  PostNotToken: requestPostNotToken,
  uploadFild: uploadFild,
  GetAddress: GetAddress,
  SetIotId: SetIotId,
  GetIotId: GetIotId,
  SetUser: SetUser,
  GetUser: GetUser,
  hexStringToArrayBuffer: hexStringToArrayBuffer,
  hexStringToBuff: hexStringToBuff,
  send0X0A: send0X0A,
  stringToArrayBuffer: stringToArrayBuffer,
  arrayBufferToString: arrayBufferToString,
  buf2hex: buf2hex
}