Component({
  data: {
    selected: 0,
    enable:true,
    "color": "#808080",
    "selectedColor": "#398BFF",
    list:[{
      "pagePath": "pages/pageConnect/pageConnect",
      "text": "连接",
      "iconPath": "/images/<EMAIL>",
      "selectedIconPath": "/images/<EMAIL>"
    },
    {
      "pagePath": "pages/pageUpdate/pageUpdate",
      "text": "升级",
      "iconPath": "/images/<EMAIL>",
      "selectedIconPath": "/images/<EMAIL>"
    },
    {
      "pagePath": "pages/pageSetting/pageSetting",
      "text": "设置",
      "iconPath": "/images/<EMAIL>",
      "selectedIconPath": "/images/<EMAIL>"
    }]
  },
  attached() {
  },
  methods: {
    switchTab(e) {
      if(!this.data.enable)return
      const data = e.currentTarget.dataset
      const url = data.path
      console.log("switchTab: " + url);
      wx.switchTab({url:"../../"+url})
      this.setData({
        selected: data.index
      })
    }
  }
})