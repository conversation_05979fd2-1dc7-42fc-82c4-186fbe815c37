/* pages/pageDownload/pageDownload.wxss */
.body {
    height:60vh;
    width:100vw;
  }
  .bmain{
      height: 99%;
      margin: 0rpx 25rpx;
      padding:0rpx 25rpx;
      background-color: white;
      border-radius: 40rpx;
  }

  .foot {
    height:25vh;
    width:100vw;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .b-title{
    font-size: 45rpx;
    font-weight: 500;
    height: 110rpx;
  }

  .main-1{
    height: 300rpx;
  }
  .main-2{
    height: 80rpx;
  }

  .main-3{
    width: 100%;
    height: 80rpx;
  }
  .main-4{
    height: 80rpx;
  }

  .main-5{
    padding-top:50rpx;  
    padding-left: 25rpx;
  }
  .firmware-detail{
      display: flex;
  }

  progress{
    width: 100%;
}


  .result1{
      display: flex;
  }

    .re3-info{
        display: flex;
    }
  .info-red{
      width: 100rpx;
  }

  .update-1{
    width: 100%;  
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .success-img , .fail-img{
      width: 220rpx;
      height: 220rpx;
  }
  .shouji-img{
    width: 140rpx;
    height: 264rpx;
  }

  .jiazai-img{
    width: 94rpx;
    height: 18rpx;
  }

  .shebei-img{
    width: 182rpx;
    height: 182rpx;
  }

.btn_4{
    height: 96rpx;
    width: 480rpx;
    font-size: 45rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    background-color: #398BFF;
    border-radius: 48rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .btn_5{
    height: 96rpx;
    width: 480rpx;
    font-size: 45rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 40rpx;
    background-color: red;
    border-radius: 48rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .btn_6{
      margin-top: 30rpx;
    height: 96rpx;
    width: 480rpx;
    font-size: 45rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: red;
    line-height: 40rpx;
    border:1rpx solid red;
    border-radius: 48rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }