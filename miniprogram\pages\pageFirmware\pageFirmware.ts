// pages/pageFirmware/pageFirmware.ts
import { IAppOption } from "../../../typings/index"
import { BluetoothManager } from "../../lib/bluetoothManager"
import { backConnectPage } from "../../lib/log"
import toast from "../../settingPageComp/toast"

const app = getApp<IAppOption>()
var sBluetoothManager: BluetoothManager

Page({
  /**
   * 页面的初始数据
   */
  data: {
    hiddenDetailModal: true,
    isUpdate: false,
    isShowDetail: false,
    deviceList: <any>[],
    firmwareName: "",
    firmwareRemark: "",
  },

  //点击返回
  returnSetp: function () {
    wx.navigateBack({ delta: 1 })
  },

  //详情弹窗的取消按钮
  onCancel: function () {
    this.setData({
      hiddenDetailModal: true
    });
  },

  //跳转微信固件升级
  wxFileUpdate: function () {
    app.globalData.connectGoToUrl = "../LB1001Update/LB1001Update"
    let device:any = sBluetoothManager.getConnectedDevice()
    console.log('连接状态：',device);
    wx.setStorageSync("showToast",false) //跳转
    if (device === null) {
      sBluetoothManager.disconnectDevice()
      if (sBluetoothManager.connectDevice(app.globalData.device, false)) {
        wx.showLoading({
          title: '重新连接中',mask:true
        })
      } else {
        wx.showModal({
          title: '提示',
          content: "请不要频繁断开连接设备",
          showCancel: true
        });
      }
    }
    else {
      wx.navigateTo({
        url: "../LB1001Update/LB1001Update"
      })
    }
  },
   //返回蓝牙链接页面。
   returnBleConunt() {
    wx.setStorageSync('isShow',true)
    wx.showModal({
      title: "温馨提示",
      content: "蓝牙已断开，请重新选择连接",
      showCancel: false,
      confirmText: "确定",
      success(res) {
        if (res.confirm) {
         wx.setStorageSync('isShow',false)
         wx.navigateBack({
           delta:backConnectPage()
         })
        }
      }
    });
  },

  //选择固件事件
  selFirmware: function (e: any) {
    console.log('选择固件',e);
    let name = e.currentTarget.dataset.name;
    if (name.split("_")[1] == app.globalData.version) {
      toast.info("不可重复选择当前版本升级")
      return;
    }
    let index = e.currentTarget.dataset.index;
    var list = this.data.deviceList;
    let bl = this.data.isUpdate;
    list.forEach(item => {
      console.log(item);
      
      if (item.firmware_id == index && item.class == "device-seled") {
        bl = false;
        item.class = "device-line";
        console.log('执行');
      }
      if (item.firmware_id == index) {
        bl = true;
        item.class = "device-seled";
      }
      if (item.file_name.split("_")[1] == app.globalData.version) {
        item.class = "device-disable"
      }
    })

    this.setData({
      isUpdate: bl,
      deviceList: list,
    })
  },

  //显示详情信息。
  showDetail: function (e: any) {
    let obj = e.currentTarget.dataset.item;
    this.setData({
      hiddenDetailModal: false,
      firmwareName: obj.file_name,
      firmwareRemark: obj.remark,
    })
  },

  //实例化固件列表
  initFirmware: function () {
    let that = this;
    var url = 'https://xk.hlktech.com/photoServer/apiv2/queryFirmwareByType?typeId=66';
    wx.request({
      url: url,
      timeout: 60000,
      success(res) {
        var obj = res.data;
        if (obj.status) {
          let list = obj.result
          console.log(list);
          list.forEach(item => {
            item.class = "device-line";
            if (item.file_name.split("_")[1] == app.globalData.version) {
              item.class = "device-disable"
            }
            item.file_size = that.roundNumber(item.file_size / 1024, 0);      //格式化内存大小
            item.createTime = that.timestampToDate(item.create_date); //格式化时间
          })
          that.setData({
            deviceList: list,
          })
        }
      },
      fail: function (res) {
        wx.hideLoading()
      }
    })
  },

  //跳转下载页面
  toDownloadPage() {
    var list = this.data.deviceList;
    var firmwareUrl = "";
    list.forEach(item => {
      if (item.class == "device-seled") {
        firmwareUrl = "https://xk.hlktech.com/photoServer/apiv2/downloadFirmwareById?firmwareId=" + item.firmware_id;
        wx.setStorageSync("firmwareUrl", firmwareUrl);
        wx.setStorageSync("firmwareName", item.file_name);
        wx.setStorageSync("firmwareSize", item.file_size);
        wx.setStorageSync("firmwareRemark", item.remark);
      }
    })

    //跳转页面。
    if (firmwareUrl) {
      let isConnected = sBluetoothManager.isConnected();
      console.log("蓝牙连接状态：" + sBluetoothManager.getConnectedDevice());
      console.log(app.globalData.device);
      wx.setStorageSync("showToast",true) //跳转
      if (!isConnected) {
        if (sBluetoothManager.connectDevice(app.globalData.device, false)) {
          wx.showLoading({
            title: '重新连接中',mask:true
          })
        } else {
          wx.showModal({
            title: '提示',
            content: "请不要频繁断开连接设备",
            showCancel: true
          });
        }//00:19:71:2A:11:B4
      }
      else {
        wx.navigateTo({
          url: "../LB1001Download/LB1001Download"
        })
      }
    }
  },
  toPageSetting(){
    wx.navigateTo({url:'../LB1001Setting/LB1001Setting'})
  },
  //四舍五入格式化数字，scale是保留多少位小数
  roundNumber(num: number, scale: number) {
    if (!("" + num).includes("e")) {
      return Math.round(num * Math.pow(10, scale)) / Math.pow(10, scale);
    } else {
      let splitNum = ("" + num).split("e");
      let roundNum = Math.round(
        +(splitNum[0] + "e" + (splitNum[1] - scale))
      );
      return roundNum / Math.pow(10, splitNum[1] - scale);
    }
  },

  // 格式化时间
  timestampToDate(timestamp: number) {
    const date = new Date(timestamp);
    const year = date.getFullYear().toString();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    return formattedDate;
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    sBluetoothManager = app.globalData.bluetoothManager;
    this.initFirmware();
  },
  onShow(){
    var vl = sBluetoothManager.isConnected()
    if (!vl) {
      console.log('执行');
      this.returnBleConunt();
      return
    }
  }
})