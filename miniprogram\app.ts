// app.ts

import { IAppOption } from "../typings/index";
import { BluetoothManager, BluetoothConfigure } from "./lib/bluetoothManager";


App<IAppOption>({
  globalData: {
    gbIsHandshake: true,
    gbIsAutoTest: false,
    gbTestNum: 1,
    gbMtuNum: 128,
    bluetoothManager: <any>null,
    device: <any>null,
    connectGoToUrl: '../pageSetting/pageSetting',
    server_token: 'HlkTech20200429',
    store: {
      device: {}, // 设备信息
      serviceId: '', // 服务ID
      characterId: '', // 特征值ID
      writeId: '', // 写入ID
      notifyId: '', // 回复ID
      response: null, //设备上报函数，可用它取消上报状态
    },//我个人使用的vuex仓库
    version:'', //固件版本
  },
  onLaunch() {
    const cacheIsHandshake = wx.getStorageSync("IsHandshake")
    if (cacheIsHandshake != "") {
      this.globalData.gbIsHandshake = cacheIsHandshake
    }
    const cacheIsAutoTest = wx.getStorageSync("IsAutoTest")
    if (cacheIsAutoTest != "") {
      this.globalData.gbIsAutoTest = cacheIsAutoTest
    }
    const cacheTestNum = wx.getStorageSync("TestNum")
    if (cacheTestNum != "") {
      this.globalData.gbTestNum = cacheTestNum
    }
    const cacheMtuNum = wx.getStorageSync("MtuNum")
    if (cacheMtuNum != "") {
      this.globalData.gbMtuNum = cacheMtuNum
    }

    const sysinfo = wx.getSystemInfoSync()
    this.globalData.bluetoothManager = new BluetoothManager(sysinfo.platform);
    const configure = new BluetoothConfigure()
    configure.isUseAuth = this.globalData.gbIsHandshake
    configure.changeMTU = this.globalData.gbMtuNum
    //todo 目前未实现自动化测试OTA
    configure.isAutoTestOTA = false;
    configure.autoTestOTACount = 20;
    this.globalData.bluetoothManager.setConfigure(configure)

    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)
  },
})