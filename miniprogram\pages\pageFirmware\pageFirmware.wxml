<!--pages/pageFirmware/pageFirmware.wxml-->
<wxs module="obj">
  var getStr15 = function(str) {
      return  str.substring(0,14);
  }
  module.exports.getStr15 = getStr15;
</wxs>

<view class="head">
    <view class="return" catchtap="returnSetp"> <image class="return-img" src="/images/fanhui.png" ></image></view>
    <text class="title">固件版本</text>
</view>
<view class="font-blue other-btn" catchtap="wxFileUpdate"> 
  微信文件升级
  <!-- <view style="margin-right: auto;text-indent: 26rpx;" catch:tap="toPageSetting">参数设置</view> -->
</view>
<view class="body">
    <view class='not-device' wx:if="{{deviceList.length == 0}}">
        <image class="not-device-img" src="/images/device-no.png" ></image>
        <view class="not-device-text font-blue">没有固件，联系管理员后台上传</view>
    </view>

    <view class="view_third" wx:if="{{deviceList.length > 0}}">
        <view class="{{item.class}}" wx:for="{{deviceList}}" wx:key="id" bindtap="onSelectedDevice"  data-item="{{item}}" data-index="{{item.firmware_id}}">
            <view class="dev-left" >
                <view class="dev-left-text"> {{item.file_name}}</view>
                <image wx:if="{{item.class == 'device-seled'}}" class="dev-left-img"  src="/images/xuanzhong.png" data-index="{{item.firmware_id}}" data-name="{{item.file_name}}" > </image>
                <image wx:if="{{item.class == 'device-line'}}"  class="dev-left-img"  src="/images/weixuanzhong.png" catchtap="selFirmware" data-index="{{item.firmware_id}}" data-name="{{item.file_name}}"> </image>
            </view>

            <view class="dev-right" >
                <view>时间：{{item.createTime}}</view>
                <view>大小：{{item.file_size}}(KB)</view>
                <view  wx:if="{{item.remark.length <= 15}}"> 内容：{{item.remark}}</view>
                <view wx:if="{{item.remark.length > 15}}" class="dev-right-detail">
                    内容：{{obj.getStr15(item.remark)}}...
                    <view catchtap="showDetail" data-item="{{item}}" class="more-btn font-blue">详情></view>
                </view>
            </view> 
        </view>
    </view>
</view>

<block wx:if="{{hiddenDetailModal === false}}">
  <modal hidden="{{hiddenDetailModal}}" title="{{firmwareName}}" confirm-text="确定" no-cancel="true"  bindconfirm="onCancel">
  <view>{{firmwareRemark}}</view> 
  </modal>
</block> 

<view class="foot" >
    <image class="foot-img"  wx:if="{{isUpdate == false}}"  src="/images/weishengji.png" > </image>
    <image class="foot-img"  wx:if="{{isUpdate == true}}"  src="/images/kaishishengji.png" catchtap="toDownloadPage"> </image>
</view>

