
export default {
  success(text ='操作成功',time=2000){
    wx.showToast({
      title:text,
      icon:'success',
      duration:time
    })
  },
  fail(text ='操作失败',time=2000){
    wx.showToast({
      title:text,
      icon:'error',
      duration:time
    })
  },
  info(text ='提示信息',time=2000){
    wx.showToast({
      title:text,
      icon:'none',
      duration:time
    })
  },
  dialog(text = '操作成功',isRouter = false){
    wx.showModal({
      title: '系统提示',
      content: text,
      showCancel: false,
      success:()=>{
        if (isRouter) {
          wx.navigateBack({
            delta:1
          })
        }
      }
    })
  }
}