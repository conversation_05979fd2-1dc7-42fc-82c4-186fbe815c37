/* components/otaProgressView.wxss */

.container {
  position: fixed;
  z-index: 10000;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.4);
}


.ota-view{
  background-color: white;
  border-radius: 30rpx;
  margin-bottom: 13%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;

  position: fixed;
  height: 400rpx;
  left: 30rpx;
  right: 30rpx;
  bottom: 30rpx;

  padding-top: 20rpx;
}


.view_0{
  width: 400rpx;
  height: 48rpx;
  font-size: 34rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  text-align: center;

  color: #242424;
  line-height: 48rpx;
}
.view_1{
  width: 300rpx;
  height: 44rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  text-align: center;
  color: #242424;
  line-height: 44rpx;
}
.view_2{
  width: 300rpx;
  height: 40rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  text-align: center;
  color: #919191;
  line-height: 40rpx;

  margin-top: -30rpx;
}
.view_3{
  width: 600rpx;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  text-align: center;
  color: #919191;
  line-height: 40rpx;
  padding-bottom: 30rpx;
}
.ota-pg{
  height: 6rpx;
  width: 86%;
}

.image_0{
  height: 104rpx;
  width: 104rpx;
}

.view-ok{
  border-top: solid #F7F7F7 2rpx;
  width: 100%;
  height: 90rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  text-align: center;
  color: #398BFF;
  line-height: 44rpx;
  margin-bottom: 0;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}





.box{
  /* width: 100px; */
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  overflow: unset;
}
/* 第二个 */

.loading{
  position: relative;
}
.loading>view{
  position: absolute;
  width: 6rpx;
  height: 18rpx;
  border-radius: 10rpx;
  background-color: #838383;
}
.loading>view:nth-child(1){
  top: 24rpx;
  left: 0rpx;
  animation: loading infinite 1s;
}
.loading>view:nth-child(2){
  top: 18.5rpx;//14.1442rpx;
  left: 18.5rpx;//14.1442rpx;
  transform: rotate(-45deg);
  animation: loading infinite 1s 0.125s;
}
.loading>view:nth-child(3){
  top: 0rpx;
  left: 24rpx;
  transform: rotate(90deg);
  animation: loading infinite 1s 0.25s;
}
.loading>view:nth-child(4){
  top: -18.5rpx;//-14.1442rpx;
  left: 18.5rpx;//14.1442rpx;
  transform: rotate(45deg);
  animation: loading infinite 1s 0.375s;
}
.loading>view:nth-child(5){
  top: -24rpx;
  left: 0rpx;
  transform: rotate(0deg);
  animation: loading infinite 1s 0.5s;
}
.loading>view:nth-child(6){
  top: -18.5rpx;//-14.1442rpx;
  left: -18.5rpx;//-14.1442rpx;
  transform: rotate(-45deg);
  animation: loading infinite 1s 0.625s;
}
.loading>view:nth-child(7){
  top: 0rpx;
  left: -24rpx;
  transform: rotate(90deg);
  animation: loading infinite 1s 0.75s;
}
.loading>view:nth-child(8){
  top: 18.5rpx; //14.1442rpx;
  left:-18.5rpx; //-14.1442rpx;
  transform: rotate(45deg);
  animation: loading infinite 1s 0.875s;
}
@keyframes loading {
  50% {
    opacity: 0.1;
  }
  100% {
    opacity: 1;
  }
}