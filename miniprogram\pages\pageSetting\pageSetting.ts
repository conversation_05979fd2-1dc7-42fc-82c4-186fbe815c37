import { IAppOption } from "../../../typings/index"
import { BleDataCallback, BleDataHandler, BleSendDataHandler } from "../../lib/ble-data-handler"
import { BluetoothManager } from "../../lib/bluetoothManager"
import toast from "../../settingPageComp/toast"
import { initBLTChange, sendAfter, sendBefore,sendData,
   string2buffer,disConnectSoBack,overSendCount, hexToDec,decimalToHexadecimal, reConnection, ab2hex } from "../../settingPageComp/index"
import AnalyzeData from "../../settingPageComp/AnalyzeData"

const app = getApp<IAppOption>()
var sBluetoothManager: BluetoothManager

Page({
  data: {
    lmdValue: 2, //灵敏度的切换value值，1选择了低，2选择了中，3高
    silderValue: 1, //滑块的值
    isGetPersonSetting: 0, //true就是选择了有人设置，默认为true
    statusText1: '无人',
    statusText2: 0, //有人-距离值（最接近）
    keepTime: 1, //无人持续时间
    version: '1.0',//查询固件版本
    // timer:0,//倒计时器，当检测无人以后用来延迟变有人
    sendCount: 0,//重试指令次数
    maxSendCount: 2,//最大重试次数
    low_lmdList: [70, 0, 70, 0, 50, 60, 50, 60, 25, 60, 25, 40, 18, 40, 18, 25, 18, 25],
    middle_lmdList: [60, 0, 60, 0, 30, 50, 30, 50, 20, 50, 20, 30, 15, 30, 15, 20, 15, 20],
    height_lmdList: [40, 0, 40, 0, 20, 40, 20, 40, 15, 40, 15, 25, 12, 25, 12, 15, 12, 15],
    isShowToast: false, //是否显示弹窗
    toastSuccess: true, //弹窗的类型 //1为蓝，0为红
    toastText: '操作成功', //弹窗文字
    strConfig: '',//页面的雷达档位对应的灵敏度的16进制数据（需要自己切换一次雷达档为再次做翻译）
    //1.页面初始化的时候需要拿到这个16进制值原始值
    //2.每滑动一次档位，就要更新一次灵敏度和原始值
    //3.每设置一次灵敏度就要重新获取一次原始值
    sendCount_lmd: 0, //发送灵敏度顺序 0 - 8 共9次，发完或者报错归0
    isStudy:true,//是否在学习模式，如果不在，设备也不回复则判定为掉线
  },
  /**打开提示窗 */
  openToast(type = 1,text = '操作成功'){
    if (type ==1 ) {
      this.setData({
        toastText:text,
        toastSuccess:true,
        isShowToast:true})
    }else{
      this.setData({
        toastText:text,
        toastSuccess:false,
        isShowToast:true})
    }
  },
  /** 进入固件版本页面 */
  toVersionPage() {
    sendBefore()
    wx.navigateTo({ url: '../pageFirmware/pageFirmware' })
  },
    /**关闭提示窗 */
  async closeToast(){  
    this.setData({isShowToast:false})
    return true
  },

  /** 设置无人持续时间 */
  setNobodyKeepTimer:function(){
    let that = this
      wx.showModal({
        title: '无人持续时间',
        editable:true,
        placeholderText:'请输入取值范围1-65535',
        success: function (res) {
          if (res.confirm) {//这里是点击了确定以后
            let data = Number(res.content)
            // 拦截非法
            if (Object.is(data, NaN)){
              toast.info('输入值只能为数字！')
              return;
            }
            if (data > 65535){
              toast.info('输入值过大!')
              return;
            }
            if (data < 1) {
              toast.info('输入值过小!')
              return;
            }
            // 执行逻辑
            that.setData({
              keepTime:data
            })
            that.setNobodyKeeptime_radarDistance_Command(data,that.data.silderValue)
          } else {//这里是点击了取消以后
            toast.info('取消操作')
          }
        }
      })
  },
  /**设置-无人持续时间 -和- 雷达距离门 */
  async setNobodyKeeptime_radarDistance_Command(_time=0,_shift=1) {
    let  resultList = []
    let time = decimalToHexadecimal(_time)
    let shift = decimalToHexadecimal(_shift)
    let data = "FD FC FB FA 14 00 60 00 0000 " + shift +"01 00 " + shift + "02 00 " + time + "04 03 02 01".replace(/\s/g, '');
    // console.log('这里',data);
    resultList[0] =  await sendBefore()
    resultList[1] =  await sendData(data)
    resultList[2] =  await sendAfter()
    console.log('打印设置距离们执行结果：',resultList);
    for (let i = 0; i < resultList.length; i++) {
      if (resultList[i] === false) { //第二段是为了防止死循环
        return toast.info("请稍后再试")
      }
    }
    this.openToast(1,'设置成功')
    wx.hideLoading()
  },
  /** 滑动-雷达距离门档位 */
  sliderchanging: function(e:any){ //滑块的取值
    let value:number = e.detail.value
    if (value === 0) {
      this.setData({
        silderValue:1
      })
    }else{
      this.setData({
        silderValue:value
      })
    }
  },
  /** 设置-雷达距离门档位 */
  bindchange:function (e:any) {
    let value:number = e.detail.value<1?1:e.detail.value //最小只能为1
    wx.showLoading({title:'设置中..',mask:true})
    this.setNobodyKeeptime_radarDistance_Command(this.data.keepTime,value)
    this.setData({
      silderValue:value,
    })
    //  setTimeout(async() => {
    //   // this.get_lmd() 不用读灵敏度
    //   await this.readCurrentDeviceConfig()
    //  }, 300);
  },
  /** 切换有无人按钮 */
  SetDevice:function(e:any){
    let status = e.target.dataset.bool
    wx.setStorageSync("showToast",false) 
    this.setData({
      isGetPersonSetting:status,
      isStudy:true
    })
    if (status == 1) {
      this.setPerson()
    }else{
      this.setNobody()
    }
  },
/** 设置有人 */ 
async setPerson(str = "FF CC 0A 00 0A BB AA"){
    this.studyMode(str,'有人')
}, 
/** 设置无人 */ 
async setNobody(str = "FF CC 09 00 09 BB AA"){
  this.studyMode(str,'无人')
},
  /** 进入学习模式指令 */
async studyMode(str:string,name:string){
  let  resultList: boolean[] = []
  resultList[0] =  await sendBefore()
  resultList[1] =  await sendData(str)
  // resultList[2] =  await sendAfter() ///等重启后再发
  console.log('打印学习模式执行结果：',resultList);
  for (let i = 0; i < resultList.length; i++) {
    if (resultList[i] === false) { 
      return toast.info("请稍后再试")
    }
  }
  // 进入学习模式成功.
  wx.showLoading({title: `${name}模式学习中`,mask:true})
  setTimeout(() => {
    wx.hideLoading({})
    this.openToast(1,`${name}学习成功`)
    setTimeout(() => {
      toast.info(`设备即将重启，请耐心等候`)
      setTimeout(() => {
        sBluetoothManager.disconnectDevice()
        this.closeToast()
        setTimeout(() => {
          this.setData({isGetPersonSetting:0})
          setTimeout(() => {
            this.reConnectionFn(1)
          }, 1500);
        }, 1500);
      }, 2000);
    }, 1000);
  }, 5500);
  },
  /** 查看固件版本 */
  async checkVersion(){
    let resultList = []
    let data = "FF CC 0B 00 0B BB AA".replace(/\s/g, '');
    resultList[0] =  await sendBefore()
    resultList[1] =  await sendData(data)
    resultList[2] =  await sendAfter()
    console.log('打印-读取版本-执行结果：',resultList); 
    for (let i = 0; i < resultList.length; i++) {
      if (resultList[i] === false) { //第二段是为了防止死循环
        return toast.info("请稍后再试")
      }
    }
    
},
  /** 切换灵敏度选择 */
  async lmdSelected(e:any){ 
    let num:number = e.target.dataset.index
    let list = []
    
    wx.showLoading({title:'设置进行中..',mask:true})
    if (e.target.dataset.index == 1) {
        list = this.data.low_lmdList
        await this.backResult_sendlmdData(list) === true?(this.setData({lmdValue:num}),this.openToast(1,'低灵敏度设置成功'))
        :this.openToast(0,'低灵敏度设置失败')
        wx.hideLoading()
    }else if (e.target.dataset.index == 2) {
        list = this.data.middle_lmdList
        await this.backResult_sendlmdData(list) === true?(this.setData({lmdValue:num}),this.openToast(1,'中灵敏度设置成功'))
        :this.openToast(0,'中灵敏度设置失败')
        wx.hideLoading()
    }else{ //3
        list = this.data.height_lmdList
        await this.backResult_sendlmdData(list) === true?(this.setData({lmdValue:num}),this.openToast(1,'高灵敏度设置成功'))
        :this.openToast(0,'高灵敏度设置失败')
        wx.hideLoading()
    }
  },
  /** 处理灵敏度设置失败 */
  async backResult_sendlmdData(list:number[]):Promise<boolean>{
    let res1 =  await sendBefore()
    console.log('开始使能:',res1);
    if (res1) { // 01.先发使能
      let failCount = 0
      return await new Promise(async(resolve)=>{
        let sendCount_lmd = 0 //初始次数
        let maxFailCount = 2 //最大重发次数

        let timer = setInterval(async ()=>{ //如果没大于9都应该继续发
          if (sendCount_lmd>=9) { //02.出口
            let res2 = await sendAfter()
            console.log('结束使能:',res2);     
            clearInterval(timer)
            return resolve(res2)
          }
          let res = await this.send_lmdData(list,sendCount_lmd)
          console.log(sendCount_lmd,'挡-回复结果：',res);
          
          if (res === false && failCount<maxFailCount) { //01.执行重发 但不能超出重发次数
            failCount+=1
            this.reSendFn(this.send_lmdData,[list,sendCount_lmd],sendCount_lmd,failCount)
          }else if (failCount>maxFailCount || res === false) { //02.超出重发次数
            failCount = 0
            sendCount_lmd = 9
            clearInterval(timer)
            return resolve(false)
          }
          else{ //03.正常走下一步，递归调用九次，调用当然在递归函数里
            failCount = 0
            sendCount_lmd++
          }
        },200)
      }) 
    }else{ //发送使能失败
      return false
    }
  },
  /**发送灵敏度指令 递归调用 */
  async send_lmdData(list:number[],sendCount_lmd:number){ //主要解决：遇到错误就打回重发
      if (sendCount_lmd < 9) {
        // let successValue = 'FD FC FB FA 04 00 64 01 00 00 04 03 02 01'
        let _sendCount_lmd = decimalToHexadecimal(sendCount_lmd)
        let sport_lmd = decimalToHexadecimal(list[sendCount_lmd*2])
        let static_lmd = decimalToHexadecimal(list[(sendCount_lmd*2)+1])
        let data = `FD FC FB FA 14 00 64 00 00 00 ${_sendCount_lmd} 01 00 ${sport_lmd} 02 00 ${static_lmd} 04 03 02 01`
        console.log( sendCount_lmd + '挡----发送的值：',_sendCount_lmd,sport_lmd,static_lmd);
        if (this.outTheFail_lmd(await sendData(data)) === false) { //02.业务
          return false
        }
        return  true
        // 走到这里说明这一档灵敏度值成功了：继续下一档灵敏度
      }
      return  false
  },
  /** 处理灵敏度错误 返回false */
  outTheFail_lmd( bool:boolean | undefined){
    if (bool === false ) { //主要关注失败，失败就打回
      return  false;
    }else{
      return true
    }
  },
  /** 重发 --灵敏度 */
  async reSendFn(fn:Function,dataList:any[],sendCount_lmd:number,failCount:number){
    console.log(`------------正在第${sendCount_lmd}距离门---正在重发${failCount}次------------------`);
    let sendCount = this.data.sendCount
    if (sendCount <3 && dataList.length != 0) {
      this.setData({sendCount:this.data.sendCount += 1})
      return await fn(...dataList)
    }if (sendCount <3 && dataList.length == 0) {
      this.setData({sendCount:this.data.sendCount += 1})
      return await fn()
    }else{
      this.setData({sendCount:0})
      return false
    //  return {status:'发送次数上限',message:'发送次数上限'}
    }
  },
  /** 读取当前设备配置 */
  async readCurrentDeviceConfig(){
    let  resultList = <any>[]
    let str = "FDFCFBFA0200610004030201"
    resultList[0] = await sendBefore()
    console.log('开始');
    
    resultList[1] = await sendData(str)
    console.log('结束');
    
    resultList[2] = await sendAfter()
    // console.log('打印读取设备执行结果：',resultList);
      for (let i = 0; i < resultList.length; i++) {
        if (resultList[i] === false ) {
          toast.info('请稍后再试')
          return;
        }
      }
      // 这里是设备读取参数成功  执行
      console.log('当前设备配置',resultList);
  },
  /*** 是否断开连接了，断开则回连 */
  isConnected(){
    var res = sBluetoothManager.isConnected()
    console.log('设备连接状态',res);
    if (res === false) { //重新连接
      toast.info('设备断开连接，正在准备回连')
      this.reConnectionFn(1) //自动重连设备
    }
  },
  /** 执行设备回连逻辑 */
  async reConnectionFn(count:number){
    if (sBluetoothManager.getConnectedDevice()?.deviceId != undefined 
    || getCurrentPages()[getCurrentPages().length -1].route.match(/pageSetting/) == null) {
       return;
    }
    console.log('执行次数',count);
    wx.showLoading({title:'正在连接'+count+'次',mask:true})
    const res:any =  await reConnection() 
      if (res === true) {
        wx.hideLoading();
        toast.info('设备已重连成功')
        return this.onShow();
      }else if ( res === false && count === 1) {
        return await this.reConnectionFn(2)
      }else if ( res === false && count === 2) {
        return await this.reConnectionFn(3)
      }else {
          disConnectSoBack('重连失败，请手动连接')
          return;
      }
  },
    /** 获取灵敏度 -设置UI */
  get_lmd(){
      let strConfig = this.data.strConfig 
      let low_lmdList = this.data.low_lmdList
      let middle_lmdList = this.data.middle_lmdList
      let height_lmdList = this.data.height_lmdList
      let static_lmd = hexToDec(strConfig.slice(30,32))
      let sport_lmd = hexToDec(strConfig.slice(48,50))
      wx.hideLoading()
      if (static_lmd === low_lmdList[0] && low_lmdList[1] === sport_lmd) {
        console.log('所以选择了低灵敏度',low_lmdList[0],low_lmdList[1]);
        this.setData({lmdValue:1}) //低灵敏度 
      }else if (static_lmd === middle_lmdList[0] && middle_lmdList[1] === sport_lmd) {
        console.log('所以选择了中灵敏度',middle_lmdList[0],middle_lmdList[1] );
        this.setData({lmdValue:2}) //中灵敏度
      }else if (static_lmd === height_lmdList[0] && height_lmdList[1] === sport_lmd) {
        console.log('所以选择了高灵敏度',height_lmdList[0] ,height_lmdList[1] );
        this.setData({lmdValue:3}) //高灵敏度
      }else  console.log('没找到合适的灵敏度：',sport_lmd,static_lmd);
  },
  //点击返回
  returnSetp: function () {
    wx.navigateBack({ delta: 10 })
  },
  /** 监听数据上报*/
  LisentResponseFn(){
    var that = this;
    let timer = 0;
    var LisentResponse:BleDataCallback = {
      async onReceiveData(res: WechatMiniprogram.OnBLECharacteristicValueChangeCallbackResult) {
          var _data =  ab2hex(res.value);
          let data =  _data.toUpperCase()  //将数据-转换-成大写
          var res1:any = await AnalyzeData.checkData(data) //这里会返回对象 -成功或失败 及指令内容
          console.log('设备回复：',data);
          //判定设备是否主动断电 --开始
          clearTimeout(timer)
          timer = setTimeout(() => {
              if (that.data.isStudy == false) {
                wx.getScreenBrightness({
                  success:(res:any)=>{
                    if (res.value != 0) {
                      disConnectSoBack()
                    }
                    console.log('当前屏幕亮度：',res.value);
                  }
                  })
                return;
              }
            }, 1000);
          //判定设备是否主动断电 --结束

            if (res1.status == 'ok') {
              that.setData({
                  statusText1:res1.statusText1, //有/无人状态
                  statusText2:res1.statusText2 //探测距离
                })
            }else if(res1.status === "设备参数"){
                if (res1.message == '读取成功') {
                    that.setData({
                      silderValue:res1.data.shift,
                      keepTime:res1.data.keepTime,
                      strConfig:res1.str, //data里有对应注释
                      sendCount:0,
                  })
                  that.get_lmd()
                }else{
                  toast.info('设备回复错误')
                }
            }else if (res1.status === "固件版本") {
              console.log('固件版本 ------>',res1.data);
              that.setData({
                  version:res1.data,
              })
              app.globalData.version = res1.data
            }else if ( res1.status === "有人模式" || res1.status === "无人模式" ) {
              that.setData({
                  isStudy:true
              })
            }
            // if (res1.status === "灵敏度"|| res1.status === "雷达距离门"  ) {
            //   //  console.log('设置灵敏度中：',res1);
            // }else{
            //   // console.log(res1);
            // }
          }
        }
      return LisentResponse

  },

  /**
    * 生命周期函数--显示时调用
    * 当小程序启动，或从后台进入前台显示，会触发 onshow，从二级页面回来时也会触发。页面显示的时候触发 从上个页面返回回来也会触发 执行顺序上 onload先触发 onshow后触发
    */
  async onShow() {
    wx.showLoading({title:'读取数据中..',mask:true})
    if (BleDataHandler.callbacks.length < 2) {
      BleDataHandler.addCallbacks(this.LisentResponseFn())
    }
    let device:any = sBluetoothManager.getConnectedDevice()
    console.log('连接状态：',device);
    if (device === null) { //如果
      return await this.reConnectionFn(1)
    }
    const res = await initBLTChange()
    console.log('初始化结果：', res);
    if (res.message === '此设备无服务') {
      wx.showModal({
        title: "系统提示",
        content:'您的设备疑似缺少主服务，是否尝试去升级完整固件？',
        showCancel: true,
        confirmText: "确定",
        success:(res)=>{
          if (res.confirm) {
            wx.navigateTo({
              url:'../pageFirmware/pageFirmware'
            })
          }else{
            wx.navigateBack({
              delta:10
            })
          }
         
        },
      });
    }
    // console.log('结束使能：',await sendAfter());
    console.log('读取设备参数：',await this.readCurrentDeviceConfig());
    console.log('读取固件版本：',await this.checkVersion());
    wx.hideLoading()
    if (getCurrentPages()[getCurrentPages().length - 1].route.match(/pageSetting/) != null) {
      this.setData({isStudy:false})
      wx.setStorageSync("showToast",true) 
    }
  },

  /**
    * 生命周期函数--监听页面加载
    */
  onLoad() {

    sBluetoothManager = app.globalData.bluetoothManager
  },
  /**
  * 生命周期函数--监听页面卸载
  页面卸载时会调用此方法，如调用redirectTo方法，navigateBack方法。
  代表一个页面生命周期的结束。
  */
 onUnload: function () {
    this.setData({isStudy:true})
    wx.setStorageSync("showToast",false) 
    console.log('跳转了');
    if (BleDataHandler.callbacks[1] != undefined) {
      console.log('移除监听');
      BleDataHandler.removeCallbacks(BleDataHandler.callbacks[1])
    }
  },
  onHide: function () {
    this.setData({isStudy:true})
    wx.setStorageSync("showToast",false) 
    console.log('小程序从前台进入后台时触发');
    if (BleDataHandler.callbacks[1] != undefined) {
      console.log('移除监听');
      BleDataHandler.removeCallbacks(BleDataHandler.callbacks[1])
    }
    // 可以在这里进行清理工作，如暂停音频、清除定时器等  
  }
})