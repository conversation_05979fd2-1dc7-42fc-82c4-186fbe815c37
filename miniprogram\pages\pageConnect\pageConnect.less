/* pages/pageConnect/pageConnect.wxss */
.not-device {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  
  .not-device-img {
    width: 370rpx;
    height: 350rpx;
  }
  .not-device-text{
      margin-top: 30rpx;
      padding-bottom: 200rpx;
  }


  .device-line {
    display: flex;
    align-items: center;
    height: 120rpx;
    margin: 25rpx;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 15rpx;
    border:1rpx solid #dbd6d6;
    border-left: 3rpx solid #dbd6d6;
    border-bottom: 6rpx solid #dbd6d6;
  }

  .dev-left-img{
      width: 80rpx;
      height: 80rpx;
  }
  
  .dev-right{
    flex-grow: 2;
    padding:0rpx 15rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .dev-right-img{
    width: 50rpx;
    height: 50rpx;
  }

  .updateInput{
    margin: 40rpx 0rpx;
    color: black;
    border: #c4c1c1 1rpx solid;
    padding:20rpx;
    border-radius: 15rpx;
  }


.view_header{
  //border: solid red 1rpx;
  background-color: white;
  height: 96rpx;
  margin-top: 20rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.view_0{
  //border: solid red 1rpx;
  width: 200rpx;
  height: 40rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  color: #242424;
  line-height: 44rpx;
}
.view_1{
  //border: solid red 1rpx;
  width: 200rpx;
  height: 40rpx;
  margin-right: 10rpx;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.text_1{
  //border: solid red 1px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-size: 30rpx;
  text-align: center;
  color: #838383;
}
.image_1{
  //border: solid red 1rpx;
  width: 32rpx;
  height: 32rpx;
}

.view_middle{
  // border: solid red 1px;
  width: 200rpx;
  height: 40rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #838383;
  line-height: 30rpx;
  margin-top: 30rpx;
  padding-left: 30rpx;
}

.view_third{
  // border: solid red 1px;
  height: 99%;
  margin-top: 20rpx;
  overflow-y: auto;
}

.itemView{
  background-color: white;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 100rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  border-bottom: solid #F4F7FB 2rpx;
}
.itemView_0{
  // border: solid red 1px;
  width: 540rpx;
}
.itemView_1{
  // border: solid red 1px;
  width: 48rpx;
  height: 48rpx;
}



