"use strict";Object.defineProperty(exports,"__esModule",{value:!0});function e(e){let R=new Date;const r=R.getFullYear(),_=R.getMonth()+1,i=R.getDate(),a=R.getHours(),s=R.getMinutes(),E=R.getSeconds(),n=R.getMilliseconds();return`${[r,_,i].map(t).join("/")} ${[a,s,E,n].map(t).join(":")}`+":HLK-OTA--\x3e"+e}const t=e=>(e=e.toString())[1]?e:`0${e}`;var R,r=3;function _(t){r<=3&&console.info(e(t))}function i(t){r<=5&&console.error(e(t))}class a{}function s(e,t){let R="";switch(e){case a.ERROR_UNKNOWN:R="Unknown error.";break;case a.ERROR_NONE:R="Success";break;case a.ERROR_INVALID_PARAM:R="Invalid parameter.";break;case a.ERROR_DATA_FORMAT:R="Data formatting error.";break;case a.ERROR_NOT_FOUND_RESOURCE:R="No resources found.";break;case a.ERROR_UNKNOWN_DEVICE:R="Unknown device.";break;case a.ERROR_DEVICE_OFFLINE:R="Device went offline.";break;case a.ERROR_IO_EXCEPTION:R="I/O exceptions occur.";break;case a.ERROR_REPEAT_STATUS:R="Repeat state.";break;case a.ERROR_RESPONSE_TIMEOUT:R="Waiting for reply command timed out.";break;case a.ERROR_REPLY_BAD_STATUS:R="Device returned a bad status.";break;case a.ERROR_REPLY_BAD_RESULT:R="Device returned an error result.";break;case a.ERROR_NONE_PARSER:R="There is no associated parser.";break;case a.ERROR_OTA_LOW_POWER:R="Low power of equipment.";break;case a.ERROR_OTA_UPDATE_FILE:R="Upgrading firmware information is error.";break;case a.ERROR_OTA_FIRMWARE_VERSION_NO_CHANGE:R="Upgrade File version must be consistent with the firmware version.";break;case a.ERROR_OTA_TWS_NOT_CONNECT:R="TWS is disconnect.";break;case a.ERROR_OTA_HEADSET_NOT_IN_CHARGING_BIN:R="The earphone is not in the charging bin.";break;case a.ERROR_OTA_DATA_CHECK_ERROR:R="Check upgrade data error.";break;case a.ERROR_OTA_FAIL:R="ota failed.";break;case a.ERROR_OTA_ENCRYPTED_KEY_NOT_MATCH:R="The encryption key does not match";break;case a.ERROR_OTA_UPGRADE_FILE_ERROR:R="The upgrade file is damaged.";break;case a.ERROR_OTA_UPGRADE_TYPE_ERROR:R="Upgrade type error.";break;case a.ERROR_OTA_LENGTH_OVER:R="A length error occurred during upgrade.";break;case a.ERROR_OTA_FLASH_IO_EXCEPTION:R="Flash read/write errors occur.";break;case a.ERROR_OTA_CMD_TIMEOUT:R="Device timed out waiting for a command.";break;case a.ERROR_OTA_IN_PROGRESS:R="OTA is in progress.";break;case a.ERROR_OTA_COMMAND_TIMEOUT:R="SDK timed out waiting for a command.";break;case a.ERROR_OTA_RECONNECT_DEVICE_TIMEOUT:R="Waiting for reconnect device timeout.";break;case a.ERROR_OTA_USE_CANCEL:R="Canceling the upgrade";break;case a.ERROR_OTA_SAME_FILE:R="Same upgrade file."}return null==t||0==t.length?R:R+"\n"+t}a.ERROR_UNKNOWN=-1,a.ERROR_NONE=0,a.ERROR_INVALID_PARAM=-2,a.ERROR_DATA_FORMAT=-3,a.ERROR_NOT_FOUND_RESOURCE=-4,a.ERROR_UNKNOWN_DEVICE=-32,a.ERROR_DEVICE_OFFLINE=-33,a.ERROR_IO_EXCEPTION=-35,a.ERROR_REPEAT_STATUS=-36,a.ERROR_RESPONSE_TIMEOUT=-64,a.ERROR_REPLY_BAD_STATUS=-65,a.ERROR_REPLY_BAD_RESULT=-66,a.ERROR_NONE_PARSER=-67,a.ERROR_OTA_LOW_POWER=-97,a.ERROR_OTA_UPDATE_FILE=-98,a.ERROR_OTA_FIRMWARE_VERSION_NO_CHANGE=-99,a.ERROR_OTA_TWS_NOT_CONNECT=-100,a.ERROR_OTA_HEADSET_NOT_IN_CHARGING_BIN=-101,a.ERROR_OTA_DATA_CHECK_ERROR=-102,a.ERROR_OTA_FAIL=-103,a.ERROR_OTA_ENCRYPTED_KEY_NOT_MATCH=-104,a.ERROR_OTA_UPGRADE_FILE_ERROR=-105,a.ERROR_OTA_UPGRADE_TYPE_ERROR=-106,a.ERROR_OTA_LENGTH_OVER=-107,a.ERROR_OTA_FLASH_IO_EXCEPTION=-108,a.ERROR_OTA_CMD_TIMEOUT=-109,a.ERROR_OTA_IN_PROGRESS=-110,a.ERROR_OTA_COMMAND_TIMEOUT=-111,a.ERROR_OTA_RECONNECT_DEVICE_TIMEOUT=-112,a.ERROR_OTA_USE_CANCEL=-113,a.ERROR_OTA_SAME_FILE=-114,exports.UpgradeType=void 0,(R=exports.UpgradeType||(exports.UpgradeType={}))[R.UPGRADE_TYPE_UNKNOWN=-1]="UPGRADE_TYPE_UNKNOWN",R[R.UPGRADE_TYPE_CHECK_FILE=0]="UPGRADE_TYPE_CHECK_FILE",R[R.UPGRADE_TYPE_FIRMWARE=1]="UPGRADE_TYPE_FIRMWARE";class E{constructor(){this.communicationWay=E.COMMUNICATION_WAY_BLE,this.isSupportNewRebootWay=!1}toString(){return"OTAConfig{communicationWay="+this.communicationWay+", isSupportNewRebootWay="+this.isSupportNewRebootWay+", updateFileDataSize="+this.updateFileData?.length+"}"}}E.COMMUNICATION_WAY_BLE=0,E.COMMUNICATION_WAY_SPP=1,E.COMMUNICATION_WAY_USB=2;class n{copy(){const e=new n;return e.isSupportNewReconnectADV=this.isSupportNewReconnectADV,e}toString(){return"ReConnectMsg{ isSupportNewReconnectADV="+this.isSupportNewReconnectADV+"}"}}class O{release(){this.callback=null}onStartOTA(){this.cbUpgradeEvent({onCallback:e=>{e.onStartOTA()}})}onNeedReconnect(e){this.cbUpgradeEvent({onCallback:t=>{t.onNeedReconnect(e)}})}onProgress(e,t){this.cbUpgradeEvent({onCallback:R=>{R.onProgress(e,t)}})}onStopOTA(){this.cbUpgradeEvent({onCallback:e=>{e.onStopOTA()}})}onCancelOTA(){this.cbUpgradeEvent({onCallback:e=>{e.onCancelOTA()}})}onError(e,t){this.cbUpgradeEvent({onCallback:R=>{R.onError(e,t)}})}cbUpgradeEvent(e){null!=this.callback&&e.onCallback(this.callback)}}class o{constructor(e){this.mUpgradeDataBuf=null,this.mTotalOTaSize=0,this.mCurrentOtaSize=0,this.mOTAConfig=null,this.mReConnectMsg=null,this.mDeviceUpgradeInfo=null,this.mTaskTimer=null,this.mReconnectTimer=null,this.mWaitDeviceOffLineTimer=null,this.mIOTAOp=e,this.mUpgradeCbHelper=new O}release(){i("release >>> OTA"),this.isOTA()&&(this.cancelOTA(),this._setOTAConfig(null)),this._resetOTAParam(),this.mUpgradeCbHelper.release()}isOTA(){return null!=this.mOTAConfig}startOTA(e,t){if(null==e||null==e.updateFileData||null!=e.updateFileData&&0==e.updateFileData.length){const e=a.ERROR_INVALID_PARAM;null!=t&&t.onError(e,s(e,""))}else if(this.mIOTAOp.isDeviceConnected())if(this.isOTA()){const e=a.ERROR_OTA_IN_PROGRESS;null!=t&&t.onError(e,s(e,"OTA is in progress. Please stop ota at first."))}else this._setOTAConfig(e),this.mUpgradeCbHelper.callback=t,this._callbackOTAStart(),null!=e.updateFileData&&e.updateFileData.length>0?this._upgradePrePare(e.updateFileData):this._callbackOTAError(a.ERROR_OTA_UPGRADE_FILE_ERROR,"startOTA : updateFileData is null or size is 0");else{const e=a.ERROR_DEVICE_OFFLINE;null!=t&&t.onError(e,s(e,""))}}cancelOTA(){if(this._checkIsNotOTA("cancelOTA"))return!1;if(!this.mIOTAOp.isDeviceConnected()){const e=a.ERROR_DEVICE_OFFLINE;return this._callbackOTAError(e,s(e,"")),!1}if(null!=this.mDeviceUpgradeInfo&&this.mDeviceUpgradeInfo.isSupportDoubleBackup){const e=this,t={onResult(){e._callbackOTACancel()},onError(t,R){e._callbackOTAError(t,R)}};return this.mIOTAOp.exitUpdateMode(t),!0}return i("cancelOTA : device is single flash ota, so ota progress cannot be interrupted."),!1}onDeviceInit(e,t){t&&null!=e&&(this.mDeviceUpgradeInfo=e),this.isOTA()&&null!=this.mReconnectTimer&&(t&&null!=e?(this._stopReConnectDeviceTimeout(),e.isMandatoryUpgrade?(this._callbackTypeAndProgress(exports.UpgradeType.UPGRADE_TYPE_FIRMWARE,0),this._enterUpdateMode()):this._callbackOTAStop()):this._callbackOTAError(a.ERROR_IO_EXCEPTION,s(a.ERROR_IO_EXCEPTION,"init device failed.")))}onDeviceDisconnect(){this.isOTA()&&(null!=this.mReConnectMsg?(_("device is offline. ready to reconnect device"),this._stopWaitDeviceOffLineTimeOut(),null==this.mReconnectTimer&&this._startWaitDeviceOffLineTimeOut(300)):this._callbackOTAError(a.ERROR_DEVICE_OFFLINE,s(a.ERROR_DEVICE_OFFLINE,"")))}notifyUpgradeSize(e,t){this.mTotalOTaSize=e,this.mCurrentOtaSize=t,this._callbackProgress(this._getCurrentProgress(e,t))}gainFileBlock(e,t){this._stopTimeoutTask();const R=this._readBlockData(e,t),r=this,_={onResult(){if(0==e&&0==t)r._queryUpgradeResult();else{if(r.mTotalOTaSize>0){let e=r.mCurrentOtaSize;e+=t,r.mCurrentOtaSize=e,r._callbackProgress(r._getCurrentProgress(r.mTotalOTaSize,r.mCurrentOtaSize))}r._startTimeoutTask()}},onError(e,t){r._callbackOTAError(e,t)}};this.mIOTAOp.receiveFileBlock(e,t,R,_)}_setOTAConfig(e){this.mOTAConfig=e}_upgradePrePare(e){if(this.mUpgradeDataBuf=e,this.mIOTAOp.isDeviceConnected())this._readUpgradeFileFlag();else{const e=a.ERROR_DEVICE_OFFLINE;this._callbackOTAError(e,s(e,""))}}_readUpgradeFileFlag(){if(this._checkIsNotOTA("_readUpgradeFileFlag"))return;const e=this,t={onResult(t){let R;if(0==t.offset&&0==t.len){R=new Uint8Array(1);const t=e.mOTAConfig?.communicationWay;R[0]=null!=t?t:0}else R=e._readBlockData(t.offset,t.len);0!=R.length?e._inquiryDeviceCanOTA(R):this.onError(a.ERROR_INVALID_PARAM,"Read Data over Limit. offset = "+t.offset+", len = "+t.len)},onError(t,R){e._callbackOTAError(t,R)}};e.mIOTAOp.readUpgradeFileFlag(t)}_inquiryDeviceCanOTA(e){if(this._checkIsNotOTA("_inquiryDeviceCanOTA"))return;_("inquiryDeviceCanOTA : >>>>>>>>>>>>");const t=this,R={onResult(e){if(_("inquiryDeviceCanOTA : onResult :  result = "+e),e==T.RESULT_CAN_UPDATE)return void t._checkUpdateEnvironment();let R,r="";switch(e){case T.RESULT_DEVICE_LOW_VOLTAGE_EQUIPMENT:R=a.ERROR_OTA_LOW_POWER;break;case T.RESULT_FIRMWARE_INFO_ERROR:R=a.ERROR_OTA_UPDATE_FILE;break;case T.RESULT_FIRMWARE_VERSION_NO_CHANGE:R=a.ERROR_OTA_FIRMWARE_VERSION_NO_CHANGE;break;case T.RESULT_TWS_NOT_CONNECT:R=a.ERROR_OTA_TWS_NOT_CONNECT;break;case T.RESULT_HEADSET_NOT_IN_CHARGING_BIN:R=a.ERROR_OTA_HEADSET_NOT_IN_CHARGING_BIN;break;default:R=a.ERROR_UNKNOWN,r=""+e}this.onError(R,s(R,r))},onError(e,R){t._callbackOTAError(e,R)}};t.mIOTAOp.inquiryDeviceCanOTA(e,R)}_checkUpdateEnvironment(){this._checkIsNotOTA("_checkUpdateEnvironment")||(null!=this.mDeviceUpgradeInfo?this.mDeviceUpgradeInfo.isSupportDoubleBackup?(this._setReConnectMsg(null),this._enterUpdateMode()):this.mDeviceUpgradeInfo.isNeedBootLoader?(this.mIOTAOp.changeReceiveMtu(),this._startTimeoutTask()):this.mDeviceUpgradeInfo.isMandatoryUpgrade?this._enterUpdateMode():this._readyToReconnectDevice():this._callbackOTAError(a.ERROR_DEVICE_OFFLINE,s(a.ERROR_DEVICE_OFFLINE,"")))}_readBlockData(e,t){if(null!=this.mUpgradeDataBuf&&this.mUpgradeDataBuf.length>0&&e+t<=this.mUpgradeDataBuf.length){const R=new Uint8Array(t);return R.set(this.mUpgradeDataBuf.slice(e,e+t)),R}return new Uint8Array(0)}_queryUpgradeResult(){if(this._checkIsNotOTA("queryUpdateResult"))return;_("queryUpdateResult : >>>>>>>>>>>>");const e=this,t={onResult(t){_("queryUpdateResult : onResult :  result = "+t);let R=0,r="";switch(t){case c.UPGRADE_RESULT_COMPLETE:return e.mIOTAOp.rebootDevice(null),void e._callbackOTAStop();case c.UPGRADE_RESULT_DOWNLOAD_BOOT_LOADER_SUCCESS:return void e._readyToReconnectDevice();case c.UPGRADE_RESULT_DATA_CHECK_ERROR:R=a.ERROR_OTA_DATA_CHECK_ERROR;break;case c.UPGRADE_RESULT_FAIL:R=a.ERROR_OTA_FAIL;break;case c.UPGRADE_RESULT_ENCRYPTED_KEY_NOT_MATCH:R=a.ERROR_OTA_ENCRYPTED_KEY_NOT_MATCH;break;case c.UPGRADE_RESULT_UPGRADE_FILE_ERROR:R=a.ERROR_OTA_UPGRADE_FILE_ERROR;break;case c.UPGRADE_RESULT_UPGRADE_TYPE_ERROR:R=a.ERROR_OTA_UPGRADE_TYPE_ERROR;break;case c.UPGRADE_RESULT_ERROR_LENGTH:R=a.ERROR_OTA_LENGTH_OVER;break;case c.UPGRADE_RESULT_FLASH_READ:R=a.ERROR_OTA_FLASH_IO_EXCEPTION;break;case c.UPGRADE_RESULT_CMD_TIMEOUT:R=a.ERROR_OTA_CMD_TIMEOUT;break;case c.UPGRADE_RESULT_SAME_FILE:R=a.ERROR_OTA_SAME_FILE;break;default:R=a.ERROR_UNKNOWN,r=""+t}this.onError(R,s(R,r))},onError(t,R){e._callbackOTAError(t,R)}};this.mIOTAOp.queryUpdateResult(t)}_enterUpdateMode(){if(this._checkIsNotOTA("enterUpdateMode"))return;const e=this,t={onResult(t){if(0==t)e._startTimeoutTask();else{const e=a.ERROR_REPLY_BAD_RESULT;this.onError(e,s(e,""+t))}},onError(t,R){e._callbackOTAError(t,R)}};this.mIOTAOp.enterUpdateMode(t)}_readyToReconnectDevice(){if(this._checkIsNotOTA("readyToReconnectDevice"))return;if(null==this.mOTAConfig)return void this._callbackOTAError(a.ERROR_OTA_FAIL," readyToReconnectDevice found OTACofig is null");const e=new n;this._setReConnectMsg(e),this._startWaitDeviceOffLineTimeOut(o.WAITING_DEVICE_OFFLINE_TIMEOUT);const t=this,R={onResult(e){null!=t.mReConnectMsg&&(t.mReConnectMsg.isSupportNewReconnectADV=0!=e)},onError(e,R){t._callbackOTAError(e,R)}};this.mIOTAOp.changeCommunicationWay(this.mOTAConfig.communicationWay,this.mOTAConfig.isSupportNewRebootWay,R)}_checkIsNotOTA(e){return!this.isOTA()&&(i(e+": Ota progress has not started yet."),!0)}_setReConnectMsg(e){this.mReConnectMsg=e}_resetOTAParam(){this.mTotalOTaSize=0,this.mCurrentOtaSize=0,this._setReConnectMsg(null),this._removeAllTimer()}_removeAllTimer(){this._stopReConnectDeviceTimeout(),this._stopTimeoutTask(),this._stopWaitDeviceOffLineTimeOut()}_startWaitDeviceOffLineTimeOut(e){this._stopWaitDeviceOffLineTimeOut();const t=this;this.mWaitDeviceOffLineTimer=setTimeout((()=>{if(this.mWaitDeviceOffLineTimer=null,i("MSG_WAIT_DEVICE_OFFLINE : timeout. isOTA = "+t.isOTA()+", "+t.mReConnectMsg),null!=t.mReConnectMsg&&t.isOTA()&&(i("MSG_RECONNECT_DEVICE : start reconnect >>>> isOTA = "+t.isOTA()+", "+t.mReConnectMsg),t.isOTA()&&null!=t.mReConnectMsg)){t.mTotalOTaSize=0,t.mCurrentOtaSize=0;const e=t.mReConnectMsg.copy();t._callbackReConnectDevice(e),t._startReConnectDeviceTimeout(e),t._setReConnectMsg(null)}}),e)}_stopWaitDeviceOffLineTimeOut(){null!=this.mWaitDeviceOffLineTimer&&(clearTimeout(this.mWaitDeviceOffLineTimer),this.mWaitDeviceOffLineTimer=null)}_startTimeoutTask(){this._stopTimeoutTask(),this.mTaskTimer=setTimeout((()=>{if(this.mTaskTimer=null,this.isOTA()){const e=a.ERROR_OTA_COMMAND_TIMEOUT;this._callbackOTAError(e,s(e,""))}}),o.WAITING_CMD_TIMEOUT)}_stopTimeoutTask(){null!=this.mTaskTimer&&(clearTimeout(this.mTaskTimer),this.mTaskTimer=null)}_startReConnectDeviceTimeout(e){this._stopReConnectDeviceTimeout(),this.mReconnectTimer=setTimeout((()=>{if(this.mReconnectTimer=null,this.isOTA()){const e=a.ERROR_OTA_RECONNECT_DEVICE_TIMEOUT;this._callbackOTAError(e,s(e,""))}}),o.RECONNECT_DEVICE_TIMEOUT)}_stopReConnectDeviceTimeout(){null!=this.mReconnectTimer&&(clearTimeout(this.mReconnectTimer),this.mReconnectTimer=null)}_callbackOTAStart(){this.mUpgradeCbHelper.onStartOTA()}_callbackProgress(e){const t=null==this.mDeviceUpgradeInfo||this.mDeviceUpgradeInfo.isNeedBootLoader?0:1;this._callbackTypeAndProgress(this._getUpgradeTypeByCode(t),e)}_callbackTypeAndProgress(e,t){this.mUpgradeCbHelper.onProgress(e,t)}_callbackReConnectDevice(e){this.mUpgradeCbHelper.onNeedReconnect(e)}_callbackOTAStop(){this._setOTAConfig(null),this._callbackProgress(100),this._resetOTAParam(),this.mUpgradeCbHelper.onStopOTA(),this.mUpgradeCbHelper.callback=null}_callbackOTACancel(){this._setOTAConfig(null),this._resetOTAParam(),this.mUpgradeCbHelper.onCancelOTA(),this.mUpgradeCbHelper.callback=null}_callbackOTAError(e,t){this._setOTAConfig(null),this._resetOTAParam(),i("callbackOTAError :  has an exception, code = "+e+", "+t),this.mUpgradeCbHelper.onError(e,t),this.mUpgradeCbHelper.callback=null}_getCurrentProgress(e,t){if(e<=0)return 0;let R=100*t/e;return R>=100&&(R=99.9),R}_getUpgradeTypeByCode(e){let t;switch(e){case 0:t=exports.UpgradeType.UPGRADE_TYPE_CHECK_FILE;break;case 1:t=exports.UpgradeType.UPGRADE_TYPE_FIRMWARE;break;default:t=exports.UpgradeType.UPGRADE_TYPE_UNKNOWN}return t}}o.WAITING_CMD_TIMEOUT=2e4,o.WAITING_DEVICE_OFFLINE_TIMEOUT=6e3,o.RECONNECT_DEVICE_DELAY=1e3,o.RECONNECT_DEVICE_TIMEOUT=8e4;class T{}T.RESULT_CAN_UPDATE=0,T.RESULT_DEVICE_LOW_VOLTAGE_EQUIPMENT=1,T.RESULT_FIRMWARE_INFO_ERROR=2,T.RESULT_FIRMWARE_VERSION_NO_CHANGE=3,T.RESULT_TWS_NOT_CONNECT=4,T.RESULT_HEADSET_NOT_IN_CHARGING_BIN=5;class c{}c.UPGRADE_RESULT_COMPLETE=0,c.UPGRADE_RESULT_DATA_CHECK_ERROR=1,c.UPGRADE_RESULT_FAIL=2,c.UPGRADE_RESULT_ENCRYPTED_KEY_NOT_MATCH=3,c.UPGRADE_RESULT_UPGRADE_FILE_ERROR=4,c.UPGRADE_RESULT_UPGRADE_TYPE_ERROR=5,c.UPGRADE_RESULT_ERROR_LENGTH=6,c.UPGRADE_RESULT_FLASH_READ=7,c.UPGRADE_RESULT_CMD_TIMEOUT=8,c.UPGRADE_RESULT_SAME_FILE=9,c.UPGRADE_RESULT_DOWNLOAD_BOOT_LOADER_SUCCESS=128,exports.DeviceUpgradeInfo=class{constructor(e,t,R){this.isSupportDoubleBackup=!1,this.isNeedBootLoader=!1,this.isMandatoryUpgrade=!1,this.isSupportDoubleBackup=e,this.isNeedBootLoader=t,this.isMandatoryUpgrade=R}},exports.FileOffset=class{constructor(e,t){this.offset=0,this.len=0,null!=e&&(this.offset=e),null!=t&&(this.len=t)}toString(){return"FileOffset{offset="+this.offset+", len="+this.len+"}"}},exports.OTAConfig=E,exports.OTAError=a,exports.OTAImpl=o,exports.ReConnectMsg=n,exports.getErrorDesc=s,exports.setLogGrade=function(e){r=e};
//# sourceMappingURL=jl_ota_2.0.0.js.map
