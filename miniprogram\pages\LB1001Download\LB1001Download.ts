// pages/pageDownload/pageDownload.ts

import { IAppOption } from "../../../typings/index"
import { BluetoothEventCallback, BluetoothManager } from "../../lib/bluetoothManager"
import { backConnectPage } from "../../lib/log"
import { OTAConfig, ReConnectMsg, UpgradeType } from "../../lib/rcsp-protocol/jl-ota/jl_ota_2.0.0"
import { disConnectSoBack, reConnection } from "../../settingPageComp/index"
import toast from "../../settingPageComp/toast"

const app = getApp<IAppOption>()
var sBluetoothManager: BluetoothManager

Page({

  /**
   * 页面的初始数据
   */
  data: {
    updateStatus: "download",  //1.download=下载中， 2.validation=效验中，3.updateImg=升级中, 4.reconnection=设备回连 5.success=成功 6.fail=失败
    updateTitle: "5-1：固件下载中...",
    isBleConnect: false,
    fileArray: <any>[],
    fileIndex: -1,
    fileOTA: <any>null,
    fileSize: 0,
    fileCrc: 0,
    showOta: false,
    isShowProgress: false,     //展示OTA升级界面
    mValue: 0,                 //进度 0-100
    mNumber: 0,                //完成次数
    mTimes: 0,                 //测试总次数
    mOtaFile: "otaUpdate.ufw", //OTA文件名
    mFailReason: "ota Fail",   //失败原因 
    mOtaResult: 0,             //0:成功 1:失败
    mStatus: 0,                 //0:检验中 1:升级中 2:回连设备 3:升级成功 4:升级失败
    firmwareName: "",
    firmwareSize: "",
    firmwareRemark: "",
    hiddenDetailModal: true,
    delta:1
  },
  upgradeData: new Uint8Array(0),
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    sBluetoothManager = app.globalData.bluetoothManager
    app.globalData.connectGoToUrl = "../LB1001Download/LB1001Download";
    const bluetoothEventCallback = new BluetoothEventCallback();
    /** 设备断开 */
    bluetoothEventCallback.onDevStatusDisconnect = (dev: WechatMiniprogram.BlueToothDevice) => {
      this.setData({
        isBleConnect: false
      })
    };
    /** 设备连接失败 */
    bluetoothEventCallback.onDevStatusFailed = (dev: WechatMiniprogram.BlueToothDevice) => {
      this.setData({
        isBleConnect: false
      })
    };
    /** 设备连接成功*/
    bluetoothEventCallback.onDevStatusSuccess = (dev: WechatMiniprogram.BlueToothDevice) => {
      this.setData({
        isBleConnect: true
      })
    };
    sBluetoothManager.addBluetoothEventCallback(bluetoothEventCallback)

    //设置固件信息
    let firmwareName = wx.getStorageSync("firmwareName");
    let firmwareSize = wx.getStorageSync("firmwareSize");  //这是Byte,
    let firmwareRemark = wx.getStorageSync("firmwareRemark");
    this.setData({
      firmwareName: firmwareName,
      firmwareSize: firmwareSize,
      firmwareRemark: firmwareRemark,
    })

    //开始下载固件 只需要执行一次
    if (wx.getStorageSync("firmwareUrl")) {
      this.downloadFirmware();
    }
  },
  onShow() {
    var vl = sBluetoothManager.isConnected()
    if (!vl) {
      this.returnBleConunt();
    }
    app.globalData.connectGoToUrl = "../LB1001Download/LB1001Download";
    this.setData({
      isBleConnect: vl,
      delta:1
    })
  },
  //四舍五入格式化数字，scale是保留多少位小数
  roundNumber(num: number, scale: number) {
    if (!("" + num).includes("e")) {
      return Math.round(num * Math.pow(10, scale)) / Math.pow(10, scale);
    } else {
      let splitNum = ("" + num).split("e");
      let roundNum = Math.round(
        +(splitNum[0] + "e" + (splitNum[1] - scale))
      );
      return roundNum / Math.pow(10, splitNum[1] - scale);
    }
  },

  //返回蓝牙链接页面。
  returnBleConunt() {
    wx.showModal({
      title: "温馨提示",
      content: "蓝牙已断开，请重新选择链接，",
      showCancel: false,
      confirmText: "确定",
      success(res) {
        if (res.confirm) {
          wx.reLaunch({
            url: "../LB1001Connect/LB1001Connect"
          })
        }
      }
    });
  },

  //详情弹窗的取消按钮
  onCancel: function () {
    this.setData({
      hiddenDetailModal: true
    });
  },

  //点击返回
  returnSetp: function () {
    let mOtaResult=this.data.mOtaResult;
    if(mOtaResult==0){
      wx.navigateBack({
        delta:3
      })
    }else{
      wx.navigateBack({
        delta:this.data.delta
      })
    }
  },
  backSetting(){
    wx.redirectTo({
      url:'../LB1001Connect/LB1001Connect'
    })
  },
  //下载固件,下载完成后会自动选择当前下载的文件
  downloadFirmware: function () {
    var that = this;
    let firmwareUrl = wx.getStorageSync("firmwareUrl");
    let firmwareName = wx.getStorageSync("firmwareName");
    wx.downloadFile({
      url: firmwareUrl,
      success: function (res) {
        // 下载成功，保存文件
        console.log("下载文件：", res);
        const savedFilePath = res.tempFilePath;
        const sizeNum = res.dataLength;
        const fs = wx.getFileSystemManager()
        fs.saveFile({
          tempFilePath: savedFilePath,
          success: function (res) {
            var myFileArray = that.data.fileArray
            let fileUrl = res.savedFilePath;
            let timeVal = Math.floor(new Date().getTime() / 1000);
            let file = { name: firmwareName, path: fileUrl, size: sizeNum, time: timeVal, type: 'file' };
            myFileArray.push({ id: 0, fileItem: file, crc: 0 })
            that.setData({
              fileArray: myFileArray
            })
            //模拟单选框选择文件
            that.onSelectedFile()
            //模拟点击升级按钮，这个操作放在读取成功的方法中去了。 that.onUpdate()

          },
          fail: function (res) {
            console.error(res.errMsg);
          }
        });
      },
      fail: function (res) {
        console.error(res.errMsg);
      }
    });
  },

  //选择文件后显示加载效果
  showLoadingView: function () {
    this.setData({
      isShowLoading: true
    })
  },
  //隐藏加载效果
  dismissLoadingView: function () {
    this.setData({
      isShowLoading: false
    })
  },

  //显示详情信息。
  showDetail: function () {
    let firmwareName = wx.getStorageSync("firmwareName");
    let firmwareRemark = wx.getStorageSync("firmwareRemark");
    this.setData({
      hiddenDetailModal: false,
      firmwareName: firmwareName,
      firmwareRemark: firmwareRemark,
    })
  },

  //点击选择文件的单选按钮。
  onSelectedFile: function () {
    let oneFile = this.data.fileArray[0]
    let fileContext = oneFile.fileItem

    this.showLoadingView()
    let fs = wx.getFileSystemManager()
    let path = fileContext.path;
    let fd = fs.openSync({
      filePath: path
    })
    let uint8 = new Uint8Array(fileContext.size);

    fs.read({
      fd: fd,
      arrayBuffer: uint8.buffer,
      length: fileContext.size,
      success: _res => {
        let tmp_crc = 0;
        uint8.forEach(it => {
          tmp_crc += it;
        })
        console.log(fileContext)

        this.upgradeData = uint8
        console.log("------------读取文件成功------------")

        setTimeout(() => {
          this.dismissLoadingView()
          wx.showToast({
            title: '加载成功',
            icon: 'none'
          })
        }, 200);
        fs.closeSync({ fd: fd })
        //加载成功后进行升级操作。
        this.onUpdate()
      },
      fail: _res => {
        this.dismissLoadingView()
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        })
        fs.closeSync({ fd: fd })
      }
    })

    this.upgradeData = oneFile.fileData
    this.setData({
      fileIndex: 0,
      fileOTA: fileContext,
      fileSize: fileContext.size,
      fileCrc: oneFile.crc,
      mOtaFile: fileContext.name,
    })
  },

  //升级主方法
  onUpdate: function () {
    console.log("准备升级==========")

    let isConnected = sBluetoothManager.isConnected();
    console.log("蓝牙连接状态：" + sBluetoothManager.isConnected());
    console.log(app.globalData.device);
    wx.setStorageSync("showToast",false) //不弹出提示断开连接
    if (!isConnected) {
      if (sBluetoothManager.connectDevice(app.globalData.device, false)) {
        wx.showLoading({
          title: '重新连接中',mask:true
        })
      } else {
        wx.showModal({
          title: '提示',
          content: "请不要频繁断开连接设备",
          showCancel: true
        });
      }
    }
    else {
      if (!this.data.isBleConnect) {
        this.returnBleConunt()
      }
    }

    if (this.data.fileIndex == -1) {
      wx.showToast({
        title: '请先选择升级文件',
        icon: 'none'
      })
      return;
    }

    console.log("开始升级=========")
    this.setData({
      isShowProgress: true
    })

    /*--- 开始执行OTA升级 ---*/
    const otaConfig: OTAConfig = new OTAConfig()
    otaConfig.isSupportNewRebootWay = true
    otaConfig.updateFileData = this.upgradeData
    // console.log("升级文件大小upgradeData size: " + this.upgradeData.length);
    sBluetoothManager.startOTA(otaConfig, {
      onStartOTA: () => {
        this.setData({
          isShowProgress: true,
          mStatus: 0,
          updateStatus: "validation",
          updateTitle: "5-2：固件效验中..."
        })
      },
      onNeedReconnect: (reConnectMsg: ReConnectMsg) => {
        this.setData({
          mValue: 0,
          mStatus: 2,
          updateStatus: "reconnection",
          updateTitle: "5-3：设备回连中...",
        })
      },
      onProgress: (type: UpgradeType, progress: number) => {
        progress = this.roundNumber(progress, 0);
        if (type == UpgradeType.UPGRADE_TYPE_CHECK_FILE) {     
          this.setData({
            mValue: progress,
            mStatus: 0,
            updateStatus: "validation",
            updateTitle: "5-2：固件效验中..."
          })
        }
        if (type == UpgradeType.UPGRADE_TYPE_FIRMWARE) {
          this.setData({
            mValue: progress,
            mStatus: 1,
            updateStatus: "updateImg",
            updateTitle: "5-4：固件升级中...",
          })
        }
      },
      onStopOTA: () => {
        this.setData({
          mValue: 100,
          mOtaResult: 0,
          mStatus: 3,
          updateStatus: "success",
          updateTitle: "5-5：升级成功",
          delta:10,
        })
      },
      onCancelOTA: () => {
        this.setData({
          mValue: 0,
          mOtaResult: 1,
          mStatus: 4,
          mFailReason: "升级被取消.",
          updateStatus: "fail",
          updateTitle: "5-5：升级异常",
        })
      },
      
      onError: (error: number, message: string) => {
        this.setData({
          mValue: 0,
          mOtaResult: 1,
          mStatus: 4,
          mFailReason: message,
          updateStatus: "fail",
          updateTitle: "5-5：升级异常",
        })
        if (message == 'Device went offline.') {
          this.returnBleConunt();
        }
      },
    })
  },
  //升级完成后，点击确定
  onOtaProgressViewConfirm: function () {
    this.setData({
      isShowProgress: false
    })
  },
})