/* pages/pageConnect/pageConnect.wxss */
.not-device {
  width: 100%;
  height: 80%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  place-items: center;
  // background-color: red;
}

.not-device-img {
  width: 280rpx;
  height: 280rpx;
  margin-bottom: 20rpx;
}

.not-device-text {
  margin-top: 30rpx;
  // padding-bottom: 200rpx;
  font-size: 32rpx;
}


.device-line {
  display: flex;
  align-items: center;
  height: 120rpx;
  margin: 25rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 15rpx;
  border: 1rpx solid #dbd6d6;
  border-left: 3rpx solid #dbd6d6;
  border-bottom: 6rpx solid #dbd6d6;
  position: relative;
}

.dev-left-img {
  margin-left: 15rpx;
  width: 80rpx;
  height: 80rpx;
}

.labelName {
  position: absolute;
  top: 20rpx;
  text-align: left;
  font-size: 32rpx;
  color: #242424;
}

.dev-right {
  margin-left: 15rpx;
  padding: 0rpx 15rpx;
  flex: 1;
  margin-right: 30rpx;
}

.dev-right-img {
  // position: absolute;
  width: 50rpx;
  height: 50rpx;
  z-index: 10;
  margin-right: 10rpx;
}

.updateInput {
  margin: 40rpx 0rpx;
  color: black;
  border: #c4c1c1 1rpx solid;
  padding: 20rpx;
  border-radius: 15rpx;
}


.view_header {
  // border: solid red 1rpx;
  background-color: white;
  height: 96rpx;
  margin-top: 20rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.view_0 {
  //border: solid red 1rpx;
  width: 200rpx;
  height: 40rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  color: #242424;
  line-height: 44rpx;
}

.view_1 {
  //border: solid red 1rpx;
  width: 200rpx;
  height: 40rpx;
  margin-right: 10rpx;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}

.text_1 {
  //border: solid red 1px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-size: 30rpx;
  text-align: center;
  color: #838383;
}

.image_1 {
  //border: solid red 1rpx;
  width: 32rpx;
  height: 32rpx;
}

.view_middle {
  // border: solid red 1px;
  width: 200rpx;
  height: 40rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #838383;
  line-height: 30rpx;
  margin-top: 30rpx;
  padding-left: 30rpx;
}

.view_third {
  margin-top: 15rpx;
  position: relative;
  // border: solid red 1px;
  height: 80%;
  overflow-y: auto;
  max-height: 70vh;
  // margin-top: 20rpx;
  overflow-y: auto;
}

.itemView {
  background-color: white;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 100rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  border-bottom: solid #F4F7FB 2rpx;
}

.itemView_0 {
  // border: solid red 1px;
  width: 540rpx;
}

.itemView_1 {
  // border: solid red 1px;
  width: 48rpx;
  height: 48rpx;
}

.body-tit {
  margin-left: 48rpx;
  font-size: 34rpx;
}

.text_2 {
  margin-left: auto;
  margin-right: 35rpx;
  font-size: 26rpx;
  letter-spacing: 1px;
  color: #7c7c7c;
}

.body-foter1 {
  // position: absolute;
  margin-top: 40rpx;
  width: 100%;
  height: fit-content;
  text-align: center;
  color: #838383;
  font-size: 28rpx;
}

.body-foter2 {
  margin-top: 30rpx;
  height: 130rpx;
  max-height: 130rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  color: #7c7c7c;
  text-indent: 50rpx;
  letter-spacing: 1px;
  padding: 0px 20rpx;
  font-size: 28rpx;
}
.updateTip{
  margin-top: -25rpx;
  margin-bottom: -20rpx;
  color: rgb(77, 77, 77);
  text-indent: 40rpx;
  font-size: 27rpx;
}

.dev-right-tit {
  position: relative;
  bottom: 20rpx;
  left: 10rpx;
}

.dev-right-text {
  position: relative;
  bottom: 0rpx;
  left: 10rpx;
}

.body-center {
  text-align: center;
  position: absolute;
  top: 15rpx;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  color: #838383;
  z-index: -1;
}

.pagebox {
  height: 85vh;
  // overflow: hidden;
  position: relative;
  // background-color: white;
  // border: 1px solid red;
}