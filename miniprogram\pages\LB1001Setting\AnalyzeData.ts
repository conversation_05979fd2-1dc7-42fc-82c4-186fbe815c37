export default {
  /** 0.先检查回复的数据是否合法 */
 async checkData(str:string):Promise<object> {
    return await new Promise( async (resolve) => {
      if(str.slice(0,2)=="4F"){
        return resolve(this.analyzeDataLog(str))
      }else{
        return resolve(this.AnalyzeDeviceConfigData(str))
      }
    })
  },
  /** 3.解析页面需要的上报数据 */ 
  async analyzeDataLog (str:string):Promise<object> {
    var res=this.hexCharCodeToStr(str);
    if(res.length>5){
      return {
        status:'ok',
        lbstatus:1,
        lblong:res.slice(10,res.length-1)
      }
    }else{
      return {
        status:'ok',
        lbstatus:0,
      }
    }
        
  },
  /** 解析当前设备参数配置 */
  async AnalyzeDeviceConfigData(str:string):Promise<object> {
    return {status:'设备参数',message:'读取成功',str}
  },
    /** 16进制转十进制且小端*/
  hexToDec(hexString:string) {
       // 删除前导0x或0X
       hexString = hexString.replace(/^0[xX]/, '');
        
        // 将十六进制数字转换为字节数组
        const bytes = [];
        for (let i = 0; i < hexString.length; i += 2) {
          bytes.push(parseInt(hexString.substr(i, 2), 16));
        }
        
        // 将字节数组转换为小端字节顺序的十进制数字
        let dec = 0;
        for (let i = 0; i < bytes.length; i++) {
          dec += bytes[i] << (i * 8);
        }
        
        return dec;
  },
  //十六进制转ascii
      hexCharCodeToStr(hexCharCodeStr:string) {
        var trimedStr = hexCharCodeStr.trim();
        var rawStr = trimedStr.substr(0, 2).toLowerCase() === "0x" ? trimedStr.substr(2) : trimedStr;
        var len = rawStr.length;
        if (len % 2 !== 0) {
            console.log("存在非法字符!");
            return "";
        }
        var curCharCode;
        var resultStr = [];
        for (var i = 0; i < len; i = i + 2) {
            curCharCode = parseInt(rawStr.substr(i, 2), 16);
            resultStr.push(String.fromCharCode(curCharCode));
        }
        return resultStr.join("");
    }
}