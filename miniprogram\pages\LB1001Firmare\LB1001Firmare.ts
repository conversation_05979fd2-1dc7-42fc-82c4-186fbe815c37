// pages/pageFirmware/pageFirmware.ts
import { IAppOption } from "../../../typings/index"
import { BluetoothManager } from "../../lib/bluetoothManager"
import { showDialog } from "../../lib/log"
import toast from "../../settingPageComp/toast"
import sign from "../../utils/sign"

const app = getApp<IAppOption>()
var sBluetoothManager: BluetoothManager

Page({
  /**
   * 页面的初始数据
   */
  data: {
    hiddenDetailModal: true,
    isUpdate: false,
    isShowDetail: false,
    deviceList: <any>[],
    firmwareName: "",
    firmwareRemark: "",
    Version:"1.0",
    type:1//1 正式固件 2 测试固件 3.定制固件
  },

  //点击返回
  returnSetp: function () {
    wx.navigateBack({ delta: 1 })
  },

  //详情弹窗的取消按钮
  onCancel: function () {
    this.setData({
      hiddenDetailModal: true
    });
  },

  //跳转微信固件升级
  wxFileUpdate: function () {
    app.globalData.connectGoToUrl = "../LB1001Update/LB1001Update"
    let device:any = sBluetoothManager.getConnectedDevice()
    console.log('连接状态：',device);
    wx.setStorageSync("showToast",false) //跳转
    if (device === null) {
      sBluetoothManager.disconnectDevice()
      if (sBluetoothManager.connectDevice(app.globalData.device, false)) {
        wx.showLoading({
          title: '重新连接中',mask:true
        })
      } else {
        wx.showModal({
          title: '提示',
          content: "请不要频繁断开连接设备",
          showCancel: true
        });
      }
    }
    else {
      wx.navigateTo({
        url: "../LB1001Update/LB1001Update"
      })
    }
  },
  //返回蓝牙链接页面。
  returnBleConunt() {
    wx.showModal({
      title: "温馨提示",
      content: "蓝牙已断开，请重新选择链接，",
      showCancel: false,
      confirmText: "确定",
      success(res) {
        if (res.confirm) {
          wx.reLaunch({
            url: "../LB1001Connect/LB1001Connect"
          })
        }
      }
    });
  },
  /** 选择固件事件 */
  onSelectedItem:function (e:any) {
    const item = e.currentTarget.dataset.item; //点击的固件
    const _class = item.class;

    var list = this.data.deviceList;
    let bl = this.data.isUpdate;
    // 只有我点的那个才有可能为true
    list.forEach(item=>item.class = "device-line" );
    // console.log('选择固件 -->',_class);
    if (_class == 'device-seled') {
      list[item.id].class = "device-line"
      bl = false;
    }else{
      list[item.id].class = "device-seled"
      bl = true;
    }
    this.setData({
      isUpdate: bl,
      deviceList: list,
    })
  },
  //显示详情信息。
  showDetail: function (e: any) {
    let obj = e.currentTarget.dataset.item;
    this.setData({
      hiddenDetailModal: false,
      firmwareName: obj.Name,
      firmwareRemark: obj.Remarks,
    })
  },

  //实例化固件列表
  initFirmware: function () {
    let that = this;
    var url = 'https://h.hlktech.com/Api/V1/Http2/GetFirmwareList';
    let header = {
      "content-type":"application/x-www-form-urlencoded; charset=UTF-8",
      name:'HLK-LB1001'
    }
    header = sign.sign(header, getApp().globalData.server_token);
    console.log(header);
    wx.request({
      url: url,
      method:'POST',
      header,
      data:{ 
          name:'HLK-LB1001',
      },
      timeout: 60000,
      success(res) {
        if (res.data?.Data) {
          let list = res.data.Data
          list.forEach((item:any,index:number) => {
            console.log('固件列表 ---> ',item);
            item.id = index
            item.class = "device-line";
            item.FileSize = that.roundNumber(item.FileSize, 0);      //格式化内存大小
            item.createTime = item.CreateTime.split(' ')[0]  //格式化时间
          })
          that.setData({
            deviceList: list,
          })
        }
      },
      fail: function (res) {
        wx.hideLoading()
      }
    })
  },

  //点击开始升级
  toDownloadPage() {
    console.log('点击开始升级');
    var list = this.data.deviceList;
    var firmwareUrl = "";
    list.forEach(item => {
      if (item.class == "device-seled") {
        firmwareUrl = item.FilePath;
        console.log('有选中',firmwareUrl);
        wx.setStorageSync("firmwareUrl", firmwareUrl);
        wx.setStorageSync("firmwareName", item.Name);
        wx.setStorageSync("firmwareSize", item.FileSize);
        wx.setStorageSync("firmwareRemark", item.Remarks);
      }
    })


    //跳转页面。
    if (firmwareUrl) {
      let isConnected = sBluetoothManager.isConnected();
      console.log("蓝牙连接状态：" + sBluetoothManager.getConnectedDevice());
      console.log(app.globalData.device);
      wx.setStorageSync("showToast",true) //跳转
      if (!isConnected) {
        if (sBluetoothManager.connectDevice(app.globalData.device, false)) {
          wx.showLoading({
            title: '重新连接中',mask:true
          })
        } else {
          wx.showModal({
            title: '提示',
            content: "请不要频繁断开连接设备",
            showCancel: true
          });
        }//00:19:71:2A:11:B4
      }
      else {
        wx.navigateTo({
          url: "../LB1001Download/LB1001Download"
        })
      }
    }
  },
  toPageSetting(){
    wx.navigateTo({url:'../pageLB1001/pageLB1001'})
  },
  //四舍五入格式化数字，scale是保留多少位小数
  roundNumber(num: number, scale: number) {
    if (!("" + num).includes("e")) {
      return Math.round(num * Math.pow(10, scale)) / Math.pow(10, scale);
    } else {
      let splitNum = ("" + num).split("e");
      let roundNum = Math.round(
        +(splitNum[0] + "e" + (splitNum[1] - scale))
      );
      return roundNum / Math.pow(10, splitNum[1] - scale);
    }
  },

  // 格式化时间
  timestampToDate(timestamp: number) {
    const date = new Date(timestamp);
    const year = date.getFullYear().toString();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    return formattedDate;
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    sBluetoothManager = app.globalData.bluetoothManager;
    this.initFirmware();
  },
  onShow(){
    let Version=wx.getStorageSync("Version");
    wx.setStorageSync("showToast",true);
    this.setData({
      Version:Version
    })
    // showDialog()
  }
})