// pages/pageUpdate/pageUpdate.ts

import { IAppOption } from "../../../typings/index"
import { BluetoothEventCallback, BluetoothManager } from "../../lib/bluetoothManager"
import { backConnectPage } from "../../lib/log"
import { OTAConfig, ReConnectMsg, UpgradeType } from "../../lib/rcsp-protocol/jl-ota/jl_ota_2.0.0"

const app = getApp<IAppOption>()
var sBluetoothManager: BluetoothManager

Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBleConnect: false,
    fileArray: <any>[],
    fileIndex: -1,
    fileOTA: <any>null,
    fileSize: 0,
    fileCrc: 0,
    showOta: false,
    isShowProgress: false,     //展示OTA升级界面
    mValue: 0,                 //进度 0-100
    mNumber: 0,                //完成次数
    mTimes: 0,                 //测试总次数
    mOtaFile: "otaUpdate.ufw", //OTA文件名
    mFailReason: "设备回复超时",   //失败原因 
    mOtaResult: 0,             //0:成功 1:失败
    mStatus: 0,                 //0:检验中 1:升级中 2:回连设备 3:升级成功 4:升级失败
    isShowLoading: false,
    mLoadingText: "加载升级文件",
    delta:1, //升级成功后会改成100反正就是要返回到首页
  },
  upgradeData: new Uint8Array(0),

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    sBluetoothManager = app.globalData.bluetoothManager
    const bluetoothEventCallback = new BluetoothEventCallback();
    /** 设备断开 */
    bluetoothEventCallback.onDevStatusDisconnect = (dev: WechatMiniprogram.BlueToothDevice) => {
      this.setData({
        isBleConnect: false
      })
    };
    /** 设备连接失败 */
    bluetoothEventCallback.onDevStatusFailed = (dev: WechatMiniprogram.BlueToothDevice) => {
      this.setData({
        isBleConnect: false
      })
    };
    /** 设备连接成功*/
    bluetoothEventCallback.onDevStatusSuccess = (dev: WechatMiniprogram.BlueToothDevice) => {
      this.setData({
        isBleConnect: true
      })
    };
    sBluetoothManager.addBluetoothEventCallback(bluetoothEventCallback)
  },
  onShow() {
    var vl = sBluetoothManager.isConnected()
    if (!vl) {
      this.returnBleConunt();
      return
    }
    this.setData({
      isBleConnect: vl,
      delta:1
    })
  },

  //点击返回
  returnSetp: function () {
    wx.navigateBack({ delta: this.data.delta })
  },
  //选择文件后显示加载效果
  showLoadingView: function () {
    this.setData({
      isShowLoading: true
    })
  },
  dismissLoadingView: function () {
    this.setData({
      isShowLoading: false
    })
  },

 //返回蓝牙链接页面。
 returnBleConunt() {
  wx.setStorageSync('isShow',true)
  wx.showModal({
    title: "温馨提示",
    content: "蓝牙已断开，请重新选择连接",
    showCancel: false,
    confirmText: "确定",
    success(res) {
      if (res.confirm) {
       wx.setStorageSync('isShow',false)
       wx.navigateBack({
         delta:backConnectPage()
       })
      }
    }
  });
},

  //点击选择文件，打开微信聊天窗口
  onAddOTAFile: function () {
    //如果蓝牙已经断开，自动跳转到连接页面。
    if (!this.data.isBleConnect) {
      this.returnBleConunt()
      return
    }

    console.log("读取文件...")
    wx.chooseMessageFile({
      count: 10,
      type: 'file',
      success: res => {
        var myFileArray = this.data.fileArray

        for (var i = 0; i < res.tempFiles.length; i++) {
          let file = res.tempFiles[i]
          console.log("--->" + file.name)
          myFileArray.push({ id: myFileArray.length, fileItem: file, crc: 0 })
        }
        this.setData({
          fileArray: myFileArray
        })
      },
      fail: e => {
        console.error(e)
        this.dismissLoadingView()

        wx.hideLoading({
          success: () => {
            wx.showToast({
              title: '数据错误',
              icon: 'none'
            })
          },
        })
      }
    })
  },

  //点击选择文件的单选按钮。
  onSelectedFile: function (e: any) {
    if (this.data.fileIndex == e.currentTarget.dataset.index) {
      this.setData({
        fileIndex: -1,
        fileOTA: null,
        fileSize: 0,
        fileCrc: 0,
        mOtaFile: "",
      })
      return;
    }

    console.log("选择的File的id:" + e.currentTarget.dataset.index)
    let oneFile = this.data.fileArray[e.currentTarget.dataset.index]
    let fileContext = oneFile.fileItem

    this.showLoadingView()
    let fs = wx.getFileSystemManager()
    let path = fileContext.path;
    let fd = fs.openSync({
      filePath: path
    })
    let uint8 = new Uint8Array(fileContext.size);

    fs.read({
      fd: fd,
      arrayBuffer: uint8.buffer,
      length: fileContext.size,
      success: _res => {
        let tmp_crc = 0;
        uint8.forEach(it => {
          tmp_crc += it;
        })
        console.log(fileContext)

        this.upgradeData = uint8
        console.log("------------读取文件成功------------")

        setTimeout(() => {
          this.dismissLoadingView()
          wx.showToast({
            title: '加载成功',
            icon: 'none'
          })
        }, 200);
        fs.closeSync({ fd: fd })
      },
      fail: _res => {
        this.dismissLoadingView()
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        })
        fs.closeSync({ fd: fd })
      }
    })

    this.upgradeData = oneFile.fileData
    this.setData({
      fileIndex: e.currentTarget.dataset.index,
      fileOTA: fileContext,
      fileSize: fileContext.size,
      fileCrc: oneFile.crc,
      mOtaFile: fileContext.name,
    })
  },

  onUpdate: function () {
    console.log("点击按钮升级1")
    if (!this.data.isBleConnect) {
      this.returnBleConunt()
      return
    }

    if (this.data.fileIndex == -1) {
      wx.showToast({
        title: '请先选择升级文件',
        icon: 'none'
      })
      return;
    }

    wx.setStorageSync("showToast",false)
    console.log("点击按钮升级2")
    this.setData({
      isShowProgress: true
    })

    /*--- 开始执行OTA升级 ---*/
    const otaConfig: OTAConfig = new OTAConfig()
    otaConfig.isSupportNewRebootWay = true
    otaConfig.updateFileData = this.upgradeData
    console.log("升级文件大小upgradeData size: " + this.upgradeData.length);
    sBluetoothManager.startOTA(otaConfig, {
      onStartOTA: () => {
        this.setData({
          isShowProgress: true,
          mStatus: 0
        })
      },
      onNeedReconnect: (reConnectMsg: ReConnectMsg) => {
        this.setData({
          mValue: 0,
          mStatus: 2
        })
      },
      onProgress: (type: UpgradeType, progress: number) => {
        if (type == UpgradeType.UPGRADE_TYPE_CHECK_FILE) {
          this.setData({
            mValue: progress,
            mStatus: 0
          })
        }
        if (type == UpgradeType.UPGRADE_TYPE_FIRMWARE) {
          this.setData({
            mValue: progress,
            mStatus: 1
          })
        }
      },
      onStopOTA: () => {
        this.setData({
          mValue: 0,
          mOtaResult: 0,
          mStatus: 3
        })
      },
      onCancelOTA: () => {
        this.setData({
          mValue: 0,
          mOtaResult: 1,
          mStatus: 4,
          mFailReason: "升级被取消."
        })
      },
      onError: (error: number, message: string) => {
        // if (message == '此固件与当前版本相同') {
          //点击事件在组件里
        // }
        this.setData({
          mValue: 0,
          mOtaResult: 1,
          mStatus: 4,
          mFailReason: message
        })
      },
    })
  },
  //升级完成后，点击确定
  onOtaProgressViewConfirm() {
    this.setData({
      isShowProgress: false,
      delta:1,
    })
    if (this.data.mStatus == 3) {
      wx.navigateBack({
        delta:backConnectPage()
      })
      return;
    }
  },

})