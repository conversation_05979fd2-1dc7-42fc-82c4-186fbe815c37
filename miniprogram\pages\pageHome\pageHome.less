/* pages/pageHome/pageHome.wxss */
.pageBox{
  width: 100%;
  height: 100vh;

}
.head{
  height: 180rpx;
  font-size: 32rpx;
  color: #302f2f;
  margin-top: -5rpx;
  // border: 1px solid ;
  // margin-bottom: 0px;
  // padding-bottom: 0px;
  // margin-bottom: 0px;
}
.main{
  margin-top: -50rpx;
  width: 100%;
  display: grid;
  grid-gap:25rpx;
  justify-content: center;
  grid-template-columns: repeat(auto-fill, 150rpx);
}
.itme{
  width: 130rpx;
  height: 130rpx;
  border-radius: 10px;
  text-align: center;
  line-height: 30rpx;
  color: white;
  box-shadow: 4px 2px 4px #ccc;
  position: relative;
  margin: 30rpx auto;
}
.logo{
  position: absolute;
  left: 0%;
  width: 100%;
  height: 100%;
  background-color: white;
  border-radius: 10px;
}
.itemName{
  position: absolute;
  top: 140rpx;
  width: 100%;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.651);
  text-align: center;
  z-index: 100;
  white-space: pre-wrap;
  word-wrap: break-word; /* 在单词边界处换行 */
  text-overflow: ellipsis;
  font-weight: bold;
  /* border: 1px solid ; */
   /* text-align: 2px 0px 2px #ccc; */
}