<!--pages/pageDownload/pageDownload.wxml-->
<wxs module="obj">
  var getStr9 = function(str) {
      return  str.substring(0,10);
  }
  module.exports.getStr9 = getStr9;
</wxs>


<view class="head">
    <view class="return" catchtap="returnSetp"> <image class="return-img" src="/images/fanhui.png" ></image></view>
    <text class="title">固件升级</text>
</view>

<view class="body">
   <view class="bmain">
      <view class="b-title flex_center1">{{updateTitle}}</view>
      <view class="main-1 flex_center1">
         <view class="update-1"
         wx:if="{{updateStatus == 'download' || updateStatus == 'validation' || updateStatus == 'updateImg' || updateStatus == 'reconnection'}}" >
            <image class="shouji-img"  src="/images/shouji.png" ></image>
            <image class="jiazai-img"  src="/images/jiazai.gif" ></image>
            <image class="shebei-img"  src="/images/SR20_shebei.png" ></image>
         </view>
         <image class="success-img" 
            wx:if="{{updateStatus == 'success'}}" 
            src="/images/chenggong.png" ></image>
         <image class="fail-img" 
            wx:if="{{updateStatus == 'fail'}}" 
            src="/images/shibai.png" ></image>
      </view>
      <!-- <view wx:if="{{updateStatus == 'success'}}" class="main-2 flex_center1 font-blue">{{updateTitle}}</view>
      <view wx:if="{{updateStatus == 'fail'}}" class="main-2 flex_center1 font-red">{{updateTitle}}</view> -->

      <view class="main-3 flex_center1" >
            <progress  wx:if="{{updateStatus == 'success' || updateStatus == 'validation'|| updateStatus == 'updateImg' }}" 
            percent="{{mValue}}" color="#398BFF" border-radius="18" show-info stroke-width="18"/>
            <progress  wx:if="{{updateStatus == 'fail'}}"  percent="{{mValue}}" color="red"      border-radius="18" show-info stroke-width="18"/>
      </view>
      <view class="main-4">
            <view class="result1"  wx:if="{{updateStatus == 'download' || updateStatus == 'validation' || updateStatus == 'updateImg' || updateStatus == 'reconnection'}}"> 
                <view class="font-red info-red">提示：</view>
                <view class="font-gray">升级过程中需要一些时间，升级过程中请耐心等待。</view>
            </view>
            <view class="result2"  wx:if="{{updateStatus == 'success'}}" > 固件版本升级成功，点击下方按钮体验吧！</view>
            <view class="result3"  wx:if="{{updateStatus == 'fail'}}" >  
                <view class="re3-info">
                    <view class="font-gray ">失败原因：</view>
                    <view> {{mFailReason}}</view>
                </view>
            </view>
      </view>

      <view class="main-5">
        <!-- <view>2、当前版本：v1.0.0</view>
        <view>3、最新版本：V1.0.1</view>  -->
        <view>1、固件名称：{{firmwareName}}</view>
        <view>2、文件大小：{{firmwareSize}}(KB)</view>
        <view  wx:if="{{firmwareRemark.length <= 11}}"> 3、说明内容：{{firmwareRemark}}</view>
        <view wx:if="{{firmwareRemark.length > 11}}" class="firmware-detail">
            3、说明内容：{{obj.getStr9(firmwareRemark)}}...
            <view catchtap="showDetail"  class="font-blue">详情></view>
        </view>
      </view>
   </view>
</view>

<view class="foot" >
    <view class="btn_4" wx:if="{{updateStatus == 'success'}}"  bindtap="backSetting">点击返回列表页面</view>
    <view wx:if="{{updateStatus == 'fail'}}">
        <view class="btn_5"  bindtap="onUpdate">立即升级</view>
        <view class="btn_6"  bindtap="returnSetp">下次再说</view>
    </view>
</view>

<!-- ======================详情弹窗========================== -->
<block wx:if="{{hiddenDetailModal === false}}">
  <modal hidden="{{hiddenDetailModal}}" title="{{firmwareName}}" confirm-text="确定" no-cancel="true"  bindconfirm="onCancel">
  <view>{{firmwareRemark}}</view> 
  </modal>
</block> 

<!-- 
    pShowOTA:Boolean,     //展示OTA升级界面
    pValue:Number,        //进度
    pNumber:Number,       //完成次数
    pTimes:Number,        //测试总次数
    pOtaFile:String,      //OTA文件名
    pFailReason:String,   //失败原因 
    pOtaResult:Number,    //0:成功 1:失败
    pStatus:Number        //0:检验中 1:升级中 2:回连设备 3:升级成功 4:升级失败
 -->
 <!-- <OTA-View 
    pShow="{{isShowProgress}}" 
    pStatus="{{mStatus}}"
    pOtaFile = "{{mOtaFile}}"
    pValue ="{{mValue}}"
    pNumber="{{mNumber}}"
    pTimes ="{{mTimes}}" 
    pOtaResult="{{mOtaResult}}"
    pFailReason="{{mFailReason}}"
    bind:OnConfirm="onOtaProgressViewConfirm">
</OTA-View> -->

<!-- <WAIT-View 
    pShow="{{isShowLoading}}" 
    pText="{{mLoadingText}}">
</WAIT-View> -->
