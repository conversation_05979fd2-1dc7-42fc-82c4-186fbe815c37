/**app.wxss**/
// .container {
//   height: 100%;
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   justify-content: space-between;
//   padding: 200rpx 0;
//   box-sizing: border-box;
// } 
/* 头部 */
.head {
    height:13vh;
    color: black;
    font-size: 40rpx;
    display: flex;
    align-items: center;
    padding-top:25rpx;
}
.body {
    height:84vh;
    width:100vw;
  }
  /* 所有也头部返回按钮,右边也padding 60像素是为了方便点击 */
  .return {
    position: absolute;
    padding: 20rpx 60rpx 0rpx 20rpx;
  }

  .return-img{
    width: 24rpx;
    height: 44rpx;
    }

  /* 所有页面的头部标题 */
  .title {
    margin: 0 auto;
  }
  .bd{
    border: 1px solid ;
  }

  /* 公共字体字体颜色 */
.font-blue{
    color:#0394F0; 
  }
  .font-gray{
    color:gray; 
  }
  .font-red{
    color:red; 
  }

  /* 按钮点击时样式 */
.hover-btn{
    -moz-box-shadow: 0 0 30rpx #0394F0;
    -webkit-box-shadow: 0 0 30rpx #0394F0;
    box-shadow: 0 0 30rpx #0394F0;
  }

  
/* 公共背景字体颜色 */
.bg-gray {
    background-color: gray;
  }
  .bg-white {                              
    background-color: white;
  }
  
  /* 公共常用布局 */
  /* 水平垂直居中：单个单行 */
  .flex_center1 {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  /* 水平垂直居中：多个单行 */
  .flex_center2 {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  
  /* 水平垂直居中：多行单个 */
  .flex_center3 {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
  }
  
  /* 水平垂直居中：多个多行 */
  .flex_center3 {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
  
  /* 字体偏移 */
  .text-left{
    text-align: left;
  }
  .text-right{
    text-align: right;
  }
  .text-center{
    text-align: center;
  }
  .y_line{
    padding-left: 8rpx;
    margin-right: 15rpx;
    height: 20rpx;
    font-size: 34rpx;
    background-color: rgb(21, 147, 231);
  }
page{
  /* background-color:  #0fa7fb; */
  height: 100vh;
  background-size: 100% 100%;
  background: linear-gradient( 186deg, #006effb2, 11%,white 40%);
}
.textOver{
  max-width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}